import 'package:lifelist/common/base_model.dart';
import 'package:lifelist/models/item_model.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/services/api_service.dart';

class AchivementService {
  // 获取我的清单
  Future<BaseModel<List<AchivementModel>>> getMyLists() async {
    final response = await ApiService().get('/lists/my');
    return BaseModel.fromJson(response, (json) => (json as List<dynamic>).map((dynamic e) => AchivementModel.fromJson(e)).toList());
  }

  // 创建清单
  Future<BaseModel<dynamic>> createList({
    required String name,
    required String description,
    String? coverImage,
  }) async {
    final response = await ApiService().post('/lists/addList', data: {
      'name': name,
      'description': description,
      'cover_image': coverImage ?? '',
    });
    return BaseModel.fromJson(response, (json) => json);
  }

  // 获取清单详情
  Future<BaseModel<AchivementModel>> getListDetail(String listId) async {
    final response = await ApiService().get('/lists/$listId');
    return BaseModel.fromJson(response, (json) => AchivementModel.fromJson(json));
  }

  // 更新清单
  Future<BaseModel<dynamic>> updateList({
    required String listId,
    String? name,
    String? description,
    String? coverImage,
  }) async {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (coverImage != null) data['cover_image'] = coverImage;

    final response = await ApiService().post('/lists/updateList/$listId', data: data);
    return BaseModel.fromJson(response, (json) => json);
  }

  // 删除清单
  Future<BaseModel<dynamic>> deleteList(String listId) async {
    final response = await ApiService().post('/lists/deleteList/$listId');
    return BaseModel.fromJson(response, (json) => json);
  }

  // 添加子项
  Future<BaseModel<AchivementItemModel>> addItem({
    required String listId,
    required String name,
  }) async {
    final response = await ApiService().post('/items', data: {
      'list_id': listId,
      'name': name,
    });
    return BaseModel.fromJson(response, (json) => AchivementItemModel.fromJson(json));
  }

  // 更新子项
  Future<BaseModel<AchivementItemModel>> updateItem({
    required String itemId,
    String? name,
    bool? isCompleted,
  }) async {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (isCompleted != null) data['is_completed'] = isCompleted;

    final response = await ApiService().post('/items/$itemId', data: data);
    return BaseModel.fromJson(response, (json) => AchivementItemModel.fromJson(json));
  }

  // 切换子项完成状态
  Future<BaseModel<dynamic>> toggleItem(String itemId) async {
    final response = await ApiService().post('/items/$itemId/toggle');
    return BaseModel.fromJson(response, (json) => json);
  }

  // 删除子项
  Future<BaseModel<dynamic>> deleteItem(String itemId) async {
    final response = await ApiService().post('/items/$itemId/delete');
    return BaseModel.fromJson(response, (json) => json);
  }

  // 分享清单到广场
  Future<BaseModel<dynamic>> shareToSquare(int listId) async {
    final response = await ApiService().post('/lists/$listId/share-to-square');
    return BaseModel.fromJson(response, (json) => json);
  }

  // 取消分享清单到广场
  Future<BaseModel<AchivementModel>> unshareFromSquare(int listId) async {
    final response = await ApiService().post('/lists/$listId/unshare-from-square');
    return BaseModel.fromJson(response, (json) => AchivementModel.fromJson(json));
  }
}
