import 'package:lifelist/common/base_model.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/services/api_service.dart';

class SquareService {
  // 获取推荐清单
  Future<BaseModel<RecommendedListResp>> getRecommendedLists() async {
    final response = await ApiService().get('/squares/recommended');
    return BaseModel.fromJson(response, (json) => RecommendedListResp.fromJson(json));
  }

  // 获取清单详情
  Future<BaseModel<SquareModel>> getSquareDetail(dynamic listId) async {
    final response = await ApiService().get('/squares/$listId');
    return BaseModel.fromJson(response, (json) => SquareModel.fromJson(json));
  }

  // 关注清单
  Future<BaseModel<dynamic>> followList(dynamic listId) async {
    final response = await ApiService().post('/squares/$listId/follow');
    return BaseModel.fromJson(response, (json) => json);
  }

  // 取消关注清单
  Future<BaseModel<dynamic>> unfollowList(dynamic listId) async {
    final response = await ApiService().post('/squares/$listId/unfollow');
    return BaseModel.fromJson(response, (json) => json);
  }
}
