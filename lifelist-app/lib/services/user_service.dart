import 'package:lifelist/common/base_model.dart';
import 'package:lifelist/models/user_model.dart';
import 'package:lifelist/services/api_service.dart';

class UserService {
  // 游客登录
  Future<BaseModel<UserModel>> guestLogin({String? uuid}) async {
    final response = await ApiService().post('/auth/guest', data: {
      if (uuid != null) 'uuid': uuid,
    });
    return BaseModel.fromJson(response, (json) => UserModel.fromJson(json));
  }

  // 华为OAuth2登录
  Future<BaseModel<UserModel>> huaweiLogin({
    required String idToken,
    required String openid,
  }) async {
    final response = await ApiService().post('/auth/huawei', data: {
      'id_token': idToken,
      'openid': openid,
    });
    return BaseModel.fromJson(response, (json) => UserModel.fromJson(json));
  }

  // 获取用户信息
  Future<BaseModel<UserModel>> getUserProfile() async {
    final response = await ApiService().get('/user/profile');
    return BaseModel.fromJson(response, (json) => UserModel.fromJson(json));
  }

  // 更新用户信息
  Future<BaseModel<UserModel>> updateUserProfile({
    String? nickname,
    String? avatar,
    String? birthdate,
  }) async {
    final data = <String, dynamic>{};
    if (nickname != null) data['nickname'] = nickname;
    if (avatar != null) data['avatar'] = avatar;
    if (birthdate != null) data['birthdate'] = birthdate;

    final response = await ApiService().put('/user/profile', data: data);
    return BaseModel.fromJson(response, (json) => UserModel.fromJson(json));
  }

  // 获取用户VIP状态
  Future<BaseModel<Map<String, dynamic>>> getUserVipStatus() async {
    final response = await ApiService().get('/user/vip');
    return BaseModel.fromJson(response, (json) => json as Map<String, dynamic>);
  }

  // 升级VIP
  Future<BaseModel<UserModel>> upgradeToVip() async {
    final response = await ApiService().post('/user/vip/upgrade');
    return BaseModel.fromJson(response, (json) => UserModel.fromJson(json));
  }

  // 获取用户统计信息
  Future<BaseModel<Map<String, dynamic>>> getUserStats() async {
    final response = await ApiService().get('/user/stats');
    return BaseModel.fromJson(response, (json) => json as Map<String, dynamic>);
  }

  // 刷新token
  Future<BaseModel<Map<String, dynamic>>> refreshToken() async {
    final response = await ApiService().post('/auth/refresh');
    return BaseModel.fromJson(response, (json) => json as Map<String, dynamic>);
  }

  // 注销账户
  Future<BaseModel<Map<String, dynamic>>> deleteAccount() async {
    final response = await ApiService().delete('/user/delete-account');
    return BaseModel.fromJson(response, (json) => json as Map<String, dynamic>);
  }
}
