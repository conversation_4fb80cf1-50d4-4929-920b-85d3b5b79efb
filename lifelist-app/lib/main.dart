import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'dart:io';
import 'common/storage_util.dart';
import 'common/loading.dart';
import 'common/user_manager.dart';
import 'router/router.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  debugPrint('start application lifelist');

  await StorageUtil.init();

  // 初始化用户管理器
  await UserManager.instance.init();

  debugPrint('start application lifelist 2');

  if (Platform.isAndroid || Platform.isIOS) {
    SystemUiOverlayStyle style = const SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.dark);
    SystemChrome.setSystemUIOverlayStyle(style);
  }
  debugPrint('start application lifelist 3');

  // 初始化API服务
  // ApiService().init();
  RouterHelper.instance;

  runApp(MaterialApp.router(
    debugShowCheckedModeBanner: false,
    title: '美瞬人生清单',
    theme: ThemeData(
      primarySwatch: Colors.blue,
      useMaterial3: true,
    ),
    localizationsDelegates: const [
      GlobalMaterialLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
    ],
    supportedLocales: const [
      Locale('en'), // English
      Locale('zh'), // Chinese
      // 添加你需要的语言
    ],
    routerConfig: RouterHelper.router,
    builder: EasyLoading.init(
      builder: (context, child) {
        return GestureDetector(
          child: child,
          onPanDown: (details) {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
              FocusManager.instance.primaryFocus?.unfocus();
            }
          },
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
              FocusManager.instance.primaryFocus?.unfocus();
            }
          },
        );
      },
    ),
  ));

  Loading.init();
}
