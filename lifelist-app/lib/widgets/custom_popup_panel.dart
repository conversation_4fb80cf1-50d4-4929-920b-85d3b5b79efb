import 'package:flutter/material.dart';
import '../constants/design.dart';
import 'submit_button.dart';

class PopupPanel extends StatefulWidget {
  final Widget widget;
  final AppBar? appBar;
  final List<Widget>? footerButtons;

  const PopupPanel(this.widget, {super.key, this.appBar, this.footerButtons});
  @override
  State<StatefulWidget> createState() => _PopupState();
}

class _PopupState extends State<PopupPanel> {
  @override
  Widget build(BuildContext context) {
    return widget.widget;
  }
}

class CustomPopupPanel extends StatefulWidget {
  final String title;
  final Widget? widget;
  final Function()? onConfirm;
  final double? heightFraction; // 屏幕高度的比例，如 0.5 表示一半高度，0.33 表示三分之一高度
  const CustomPopupPanel(
      {required this.title,
      required this.widget,
      this.onConfirm,
      this.heightFraction = 0.5, // 默认为屏幕的一半高度
      super.key});

  @override
  State<StatefulWidget> createState() => _CustomPopupState();
}

class _CustomPopupState extends State<CustomPopupPanel> {
  @override
  void initState() {
    super.initState();
  }
// 在 _CustomPopupState 类中

  @override
  Widget build(BuildContext context) {
    // 核心：
    // 1. 外层 Padding 依然负责响应键盘，将整个内容向上推。
    // 2. 内层 Column 使用 mainAxisSize: MainAxisSize.min 来实现高度自适应。
    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end, // 确保弹窗在没有键盘时也贴在底部
        mainAxisSize: MainAxisSize.min, // ✅ 关键！让 Column 的高度由子元素决定
        children: [
          // 使用 Flexible 来防止内容过长时溢出屏幕
          Flexible(
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              child: Container(
                color: MColor.xFFF5F5F5,
                // 如果内容可能超出屏幕高度，SingleChildScrollView 可以使其滚动
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 1. 自定义 AppBar (回归到最初的 Row 实现)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                        child: Row(
                          children: [
                            const Spacer(),
                            Text(
                              widget.title,
                              style: const TextStyle(
                                fontSize: 16,
                                color: MColor.xFF1B1C1A,
                                height: 1.4,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: const Icon(Icons.close, size: 22, color: MColor.xFF999999),
                            ),
                          ],
                        ),
                      ),
                      // 2. 主要内容区域
                      Container(
                        padding: const EdgeInsets.only(
                          left: 14,
                          right: 14,
                          top: 0,
                          bottom: 16, // 为底部按钮和安全区域留出空间
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (widget.widget != null) widget.widget!,
                            if (widget.onConfirm != null) ...{
                              const SizedBox(height: 24),
                              SubmitButton(
                                onTap: () {
                                  widget.onConfirm?.call();
                                  if (Navigator.canPop(context)) {
                                    Navigator.pop(context);
                                  }
                                },
                              ),
                            },
                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
