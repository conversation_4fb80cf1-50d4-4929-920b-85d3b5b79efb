import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../constants/design.dart';

class WrappedCachedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;

  const WrappedCachedNetworkImage(this.imageUrl, {this.width, this.height, this.fit, super.key});

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: MColor.xFFECECEC,
        ),
      );
    }
    if (Platform.isOhos) {
      return Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: MColor.xFFECECEC,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            decoration: BoxDecoration(
              color: MColor.skin,
            ),
          );
        },
      );
    }
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (ctx, e) {
        return Container(
          decoration: BoxDecoration(
            color: MColor.xFFECECEC,
          ),
        );
      },
      errorWidget: (ctx, e, x) {
        return Container(
          decoration: BoxDecoration(
            color: MColor.skin,
          ),
        );
      },
    );
  }
}
