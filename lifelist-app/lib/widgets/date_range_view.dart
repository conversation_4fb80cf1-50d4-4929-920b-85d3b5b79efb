import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/design.dart';
import '../tools/tools.dart';

class DateRangeView extends StatelessWidget {
  final int dateRangeType;
  final DateTime dateTimeStart;
  final DateTime dateTimeEnd;
  final Function(int, DateTime, DateTime) onDateRangeChanged;
  final bool? showQuickSelect;

  const DateRangeView(
      {required this.dateRangeType, required this.dateTimeStart, required this.dateTimeEnd, required this.onDateRangeChanged, this.showQuickSelect, super.key});

  @override
  Widget build(BuildContext context) {
    List<Widget> timeRanges = [];
    ['日', '周', '月', '年'].asMap().forEach(
      (index, element) {
        timeRanges.add(GestureDetector(
          onTap: () {
            DateTime now = DateTime.now();
            DateTime begin = DateTime.now();
            DateTime end = DateTime.now();
            if (index == 0) {
              begin = now;
              end = now;
            } else if (index == 1) {
              begin = now.subtract(Duration(days: now.weekday - 1));
              end = begin.add(const Duration(days: 6));
            } else if (index == 2) {
              begin = DateTime(now.year, now.month, 1);
              end = DateTime(now.year, now.month + 1, 0);
            } else if (index == 3) {
              begin = DateTime(now.year, 1, 1);
              end = DateTime(now.year + 1, 1, 0);
            }
            // _getData();
            onDateRangeChanged(index, begin, end);
          },
          child: Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(16), color: dateRangeType == index ? MColor.skin : MColor.xFFFFFFFF),
              width: 60,
              child: Center(
                child: Text(
                  element,
                  style: TextStyle(fontSize: 15, height: 1.4, color: dateRangeType == index ? MColor.xFFFFFFFF : MColor.xFF1B1C1A),
                ),
              )),
        ));
      },
    );
    return Column(
      children: [
        Container(
          decoration: const BoxDecoration(),
          height: 40,
          child: Row(
            children: [
              const SizedBox(
                width: 14,
              ),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: MColor.xFFFFFFFF),
                child: Row(
                  children: timeRanges,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                          context: context,
                          builder: (context) => _DateRangePanel(dateTimeStart, dateTimeEnd),
                          // isScrollControlled: true,
                          useRootNavigator: true)
                      .then((value) {
                    if (value != null) {
                      DateTime begin = value['start'];
                      DateTime end = value['end'];
                      // dateTimeStart = value['start'];
                      // dateTimeEnd = value['end'];
                      // dateRangeType = 4;
                      // _getData();
                      onDateRangeChanged(4, begin, end);
                    }
                  });
                },
                child: Text(
                  '自定义',
                  style: TextStyle(fontSize: 16, height: 1.4, color: dateRangeType == 4 ? MColor.xFF315C5B : MColor.skin),
                ),
              ),
              const SizedBox(
                width: 14,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 18,
        ),
        Container(color: Colors.transparent, child: _timeRangeView(context))
      ],
    );
  }

  Widget _timeRangeView(BuildContext context) {
    if (dateRangeType == 0) {
      String dateStr = '';
      DateTime dtNow = DateTime.now();
      List<dynamic> quickDates = [];
      DateFormat dfDay = DateFormat('d日');
      quickDates.add({'value': dfDay.format(dateTimeStart.subtract(const Duration(days: 3))), 'begin': dateTimeStart.subtract(const Duration(days: 3))});
      quickDates.add({'value': dfDay.format(dateTimeStart.subtract(const Duration(days: 2))), 'begin': dateTimeStart.subtract(const Duration(days: 2))});
      quickDates.add({'value': dfDay.format(dateTimeStart.subtract(const Duration(days: 1))), 'begin': dateTimeStart.subtract(const Duration(days: 1))});
      if (dtNow.year == dateTimeStart.year && dtNow.month == dateTimeStart.month && dtNow.day == dateTimeStart.day) {
        dateStr = '本日';
      } else {
        DateFormat df = DateFormat('M月d日');
        dateStr = df.format(dateTimeStart);
      }
      List<Widget> quickWidgets = [];
      quickDates.asMap().forEach(
        (key, value) {
          quickWidgets.add(GestureDetector(
            onTap: () {
              onDateRangeChanged(dateRangeType, value['begin'], value['begin']);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                value['value'],
                style: const TextStyle(fontSize: 13, height: 1.4, color: MColor.xFF315C5B),
              ),
            ),
          ));
        },
      );
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          mainAxisAlignment: showQuickSelect == true ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
          children: [
            if (showQuickSelect == true) ...{
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [...quickWidgets],
                ),
              ),
              const SizedBox(
                width: 32,
              )
            },
            Expanded(
              flex: 0,
              child: Row(
                children: [
                  IconButton(
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                      style: const ButtonStyle(
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                      ),
                      onPressed: () {
                        // setState(() {
                        //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 1);
                        //   dateTimeEnd = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day - 1);
                        //   _loadData();
                        // });
                        DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 1);
                        DateTime end = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day - 1);
                        onDateRangeChanged(dateRangeType, begin, end);
                      },
                      icon: Image.asset(
                        'assets/images/ic_left.png',
                        width: 20,
                        height: 20,
                        fit: BoxFit.fill,
                      )),
                  const SizedBox(
                    width: 32,
                  ),
                  Text(
                    dateStr,
                    style: const TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF315C5B),
                  ),
                  const SizedBox(
                    width: 32,
                  ),
                  IconButton(
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                      style: const ButtonStyle(
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                      ),
                      onPressed: () {
                        // setState(() {
                        //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day + 1);
                        //   dateTimeEnd = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day + 1);
                        //   _loadData();
                        // });
                        DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day + 1);
                        DateTime end = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day + 1);
                        onDateRangeChanged(dateRangeType, begin, end);
                      },
                      icon: Image.asset(
                        'assets/images/ic_right.png',
                        width: 20,
                        height: 20,
                        fit: BoxFit.fill,
                      )),
                ],
              ),
            )
          ],
        ),
      );
    } else if (dateRangeType == 1) {
      String weekStr = '';
      DateTime dtNow = DateTime.now();
      DateTime dtNowWeekBegin = dtNow.subtract(Duration(days: dtNow.weekday - 1));
      if (dtNowWeekBegin.year == dateTimeStart.year && dtNowWeekBegin.month == dateTimeStart.month && dtNowWeekBegin.day == dateTimeStart.day) {
        weekStr = '本周';
      } else {
        DateFormat df = DateFormat('M月d日');
        String weekBeginStr = df.format(dateTimeStart);
        String weekEndStr = df.format(dateTimeEnd);
        weekStr = '$weekBeginStr - $weekEndStr';
      }
      return Row(
        children: [
          const Spacer(),
          IconButton(
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(),
              style: const ButtonStyle(
                tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
              ),
              onPressed: () {
                // setState(() {
                //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 7);
                //   dateTimeEnd = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day - 7);
                //   _loadData();
                // });
                DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 7);
                DateTime end = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day - 7);
                onDateRangeChanged(dateRangeType, begin, end);
              },
              icon: Image.asset(
                'assets/images/ic_left.png',
                width: 20,
                height: 20,
                fit: BoxFit.fill,
              )),
          const SizedBox(
            width: 32,
          ),
          Text(
            weekStr,
            style: const TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF315C5B),
          ),
          const SizedBox(
            width: 32,
          ),
          IconButton(
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(),
              style: const ButtonStyle(
                tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
              ),
              onPressed: () {
                // setState(() {
                //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day + 7);
                //   dateTimeEnd = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day + 7);
                //   _loadData();
                // });
                DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day + 7);
                DateTime end = DateTime(dateTimeEnd.year, dateTimeEnd.month, dateTimeEnd.day + 7);
                onDateRangeChanged(dateRangeType, begin, end);
              },
              icon: Image.asset(
                'assets/images/ic_right.png',
                width: 20,
                height: 20,
                fit: BoxFit.fill,
              )),
          const Spacer(),
        ],
      );
    } else if (dateRangeType == 2) {
      String monthStr = '';
      DateTime dtNow = DateTime.now();
      List<dynamic> quickDates = [];
      DateFormat dfMonth = DateFormat('M月');
      DateTime tmpDate = DateTime(dateTimeStart.year, dateTimeStart.month - 3, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year, tmpDate.month + 1, 1).subtract(Duration(days: 1))});
      tmpDate = DateTime(dateTimeStart.year, dateTimeStart.month - 2, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year, tmpDate.month + 1, 1).subtract(Duration(days: 1))});
      tmpDate = DateTime(dateTimeStart.year, dateTimeStart.month - 1, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year, tmpDate.month + 1, 1).subtract(Duration(days: 1))});
      List<Widget> quickWidgets = [];
      quickDates.asMap().forEach(
        (key, value) {
          quickWidgets.add(GestureDetector(
            onTap: () {
              onDateRangeChanged(dateRangeType, value['begin'], value['end']);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                value['value'],
                style: const TextStyle(fontSize: 13, height: 1.4, color: MColor.xFF315C5B),
              ),
            ),
          ));
        },
      );
      if (dtNow.year == dateTimeStart.year && dtNow.month == dateTimeStart.month) {
        monthStr = '本月';
      } else if (dtNow.year == dateTimeStart.year) {
        DateFormat df = DateFormat('M月');
        monthStr = df.format(dateTimeStart);
      } else {
        DateFormat df = DateFormat('yyyy年M月');
        monthStr = df.format(dateTimeStart);
      }
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          mainAxisAlignment: showQuickSelect == true ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
          children: [
            if (showQuickSelect == true) ...{
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [...quickWidgets],
                ),
              ),
              const SizedBox(
                width: 32,
              )
            },
            Expanded(
                flex: 0,
                child: Row(
                  children: [
                    IconButton(
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        onPressed: () {
                          // setState(() {
                          //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month - 1, 1);
                          //   dateTimeEnd = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 1);
                          //   _loadData();
                          // });
                          DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month - 1, 1);
                          DateTime end = DateTime(begin.year, begin.month + 1, 1).subtract(const Duration(days: 1));
                          onDateRangeChanged(dateRangeType, begin, end);
                        },
                        icon: Image.asset(
                          'assets/images/ic_left.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.fill,
                        )),
                    const SizedBox(
                      width: 32,
                    ),
                    Text(
                      monthStr,
                      style: const TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF315C5B),
                    ),
                    const SizedBox(
                      width: 32,
                    ),
                    IconButton(
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        onPressed: () {
                          // setState(() {
                          //   dateTimeStart = DateTime(dateTimeStart.year, dateTimeStart.month + 1, 1);
                          //   dateTimeEnd = DateTime(dateTimeStart.year, dateTimeStart.month, dateTimeStart.day - 1);
                          //   _loadData();
                          // });
                          DateTime begin = DateTime(dateTimeStart.year, dateTimeStart.month + 1, 1);
                          DateTime end = DateTime(begin.year, begin.month + 1, 1).subtract(Duration(days: 1));
                          onDateRangeChanged(dateRangeType, begin, end);
                        },
                        icon: Image.asset(
                          'assets/images/ic_right.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.fill,
                        )),
                  ],
                ))
          ],
        ),
      );
    } else if (dateRangeType == 3) {
      String yearStr = '';
      DateTime dtNow = DateTime.now();
      if (dtNow.year == dateTimeStart.year) {
        yearStr = '本年';
      } else {
        DateFormat df = DateFormat('yyyy年');
        yearStr = df.format(dateTimeStart);
      }

      List<dynamic> quickDates = [];
      DateFormat dfMonth = DateFormat('yy年');
      DateTime tmpDate = DateTime(dateTimeStart.year - 3, 1, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year + 1, 1, 1).subtract(Duration(days: 1))});
      tmpDate = DateTime(dateTimeStart.year, dateTimeStart.month - 2, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year + 1, 1, 1).subtract(Duration(days: 1))});
      tmpDate = DateTime(dateTimeStart.year, dateTimeStart.month - 1, 1);
      quickDates.add({'value': dfMonth.format(tmpDate), 'begin': tmpDate, 'end': DateTime(tmpDate.year + 1, 1, 1).subtract(Duration(days: 1))});
      List<Widget> quickWidgets = [];
      quickDates.asMap().forEach(
        (key, value) {
          quickWidgets.add(GestureDetector(
            onTap: () {
              onDateRangeChanged(dateRangeType, value['begin'], value['end']);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                value['value'],
                style: const TextStyle(fontSize: 13, height: 1.4, color: MColor.xFF315C5B),
              ),
            ),
          ));
        },
      );
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          mainAxisAlignment: showQuickSelect == true ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
          children: [
            if (showQuickSelect == true) ...{
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [...quickWidgets],
                ),
              ),
              const SizedBox(
                width: 32,
              )
            },
            Expanded(
                flex: 0,
                child: Row(
                  children: [
                    IconButton(
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        onPressed: () {
                          // setState(() {
                          //   dateTimeStart = DateTime(dateTimeStart.year - 1, 1, 1);
                          //   dateTimeEnd = DateTime(dateTimeStart.year + 1, 1, dateTimeStart.year - 1);
                          //   _loadData();
                          // });
                          DateTime begin = DateTime(dateTimeStart.year - 1, 1, 1);
                          DateTime end = DateTime(begin.year + 1, 1, 1).subtract(const Duration(days: 1));
                          onDateRangeChanged(dateRangeType, begin, end);
                        },
                        icon: Image.asset(
                          'assets/images/ic_left.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.fill,
                        )),
                    const SizedBox(
                      width: 32,
                    ),
                    Text(
                      yearStr,
                      style: const TextStyle(fontSize: 16, height: 1.4, color: MColor.xFF315C5B),
                    ),
                    const SizedBox(
                      width: 32,
                    ),
                    IconButton(
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        onPressed: () {
                          // setState(() {
                          //   dateTimeStart = DateTime(dateTimeStart.year + 1, 1, 1);
                          //   dateTimeEnd = DateTime(dateTimeStart.year + 1, 1, dateTimeStart.year - 1);
                          //   _loadData();
                          // });
                          DateTime begin = DateTime(dateTimeStart.year + 1, 1, 1);
                          DateTime end = DateTime(begin.year + 1, 1, 1).subtract(Duration(days: 1));
                          onDateRangeChanged(dateRangeType, begin, end);
                        },
                        icon: Image.asset(
                          'assets/images/ic_right.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.fill,
                        )),
                  ],
                ))
          ],
        ),
      );
    } else {
      DateFormat df = DateFormat('yyyy-MM-dd');
      return GestureDetector(
        onTap: () {
          showModalBottomSheet(
                  context: context,
                  builder: (context) => _DateRangePanel(dateTimeStart, dateTimeEnd),
                  /*  isScrollControlled: true, isScrollControlled: true, */ useRootNavigator: true)
              .then((value) {
            if (value != null) {
              // dateTimeStart = value['start'];
              // dateTimeEnd = value['end'];
              // dateRangeType = 4;
              // setState(() {});
              DateTime begin = value['start'];
              DateTime end = value['end'];
              onDateRangeChanged(4, begin, end);
            }
          });
        },
        child: Row(
          children: [
            const SizedBox(
              width: 14,
            ),
            Expanded(
              child: Container(
                height: 32,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(10), border: Border.all(color: MColor.skin, width: 1)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      dateTimeStart == null ? '开始日期' : df.format(dateTimeStart!),
                      style: const TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                    ),
                    const Spacer(),
                    IconButton(
                        padding: EdgeInsets.zero,
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        constraints: const BoxConstraints(),
                        onPressed: () {
                          // TODO: Navigate to calendar page
                        },
                        icon: Image.asset('assets/images/ic_calendar.png', width: 16, height: 16))
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 9,
            ),
            Container(
              width: 26,
              decoration: BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(1), border: Border.all(width: 1, color: MColor.xFF68C2BF)),
            ),
            const SizedBox(
              width: 9,
            ),
            Expanded(
              child: Container(
                height: 32,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(10), border: Border.all(color: MColor.skin, width: 1)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      dateTimeEnd == null ? '结束日期' : df.format(dateTimeEnd!),
                      style: const TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                    ),
                    const Spacer(),
                    IconButton(
                        padding: EdgeInsets.zero,
                        style: const ButtonStyle(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                        ),
                        constraints: const BoxConstraints(),
                        onPressed: () {
                          // TODO: Navigate to calendar page
                        },
                        icon: Image.asset('assets/images/ic_calendar.png', width: 16, height: 16))
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 14,
            ),
          ],
        ),
      );
    }
  }
}

class _DateRangePanel extends StatefulWidget {
  final DateTime? dateStart;
  final DateTime? dateEnd;

  const _DateRangePanel(this.dateStart, this.dateEnd);

  @override
  State<StatefulWidget> createState() => _DateRangeState();
}

class _DateRangeState extends State<_DateRangePanel> {
  DateTime? _tmpDateStart;
  DateTime? _tmpDateEnd;

  @override
  void initState() {
    _tmpDateStart = widget.dateStart;
    _tmpDateEnd = widget.dateEnd;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant _DateRangePanel oldWidget) {
    _tmpDateStart = widget.dateStart;
    _tmpDateEnd = widget.dateEnd;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    DateFormat df = DateFormat('yyyy-MM-dd');

    return Theme(
        data: Theme.of(context).copyWith(
          dividerTheme: const DividerThemeData(
            color: Colors.transparent,
          ),
        ),
        child: ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            child: Scaffold(
                backgroundColor: MColor.xFFF5F5F5,
                appBar: AppBar(
                  backgroundColor: MColor.xFFF5F5F5,
                  title: const Text('自定义时间', style: TextStyle(height: 1.4, fontSize: 16, color: MColor.xFF1B1C1A)),
                  scrolledUnderElevation: 0,
                  centerTitle: true,
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: const Icon(Icons.close, size: 22, color: MColor.xFF999999))
                  ],
                ),
                persistentFooterButtons: [
                  GestureDetector(
                    onTap: () {
                      if (_tmpDateStart == null) {
                        showToast('请选择起始时间');
                        return;
                      }
                      if (_tmpDateEnd == null) {
                        showToast('请选择结束时间');
                        return;
                      }

                      Duration diff = _tmpDateEnd!.difference(_tmpDateStart!);
                      if (diff.inDays > 365) {
                        showToast('不可超过365天');
                        return;
                      }
                      Navigator.of(context).pop({'start': _tmpDateStart!, 'end': _tmpDateEnd!});
                      // Get.back(result: {'start': _tmpDateStart!, 'end': _tmpDateEnd!});
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          gradient: const LinearGradient(
                              begin: FractionalOffset(0.0, 0.0), end: FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '确认',
                            style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
                body: SafeArea(
                    child: Row(
                  children: [
                    const SizedBox(
                      width: 14,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          showDatePicker(
                                  locale: const Locale('zh', 'CN'), context: context, firstDate: DateTime(2020, 1, 1), lastDate: _tmpDateEnd ?? DateTime.now())
                              .then((date) {
                            if (date != null) {
                              setState(
                                () {
                                  _tmpDateStart = date;
                                },
                              );
                            }
                          });
                        },
                        child: Container(
                          height: 32,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration:
                              BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(10), border: Border.all(color: MColor.skin, width: 1)),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _tmpDateStart == null ? '开始日期' : df.format(_tmpDateStart!),
                                style: const TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                              ),
                              const Spacer(),
                              IconButton(
                                  padding: EdgeInsets.zero,
                                  style: const ButtonStyle(
                                    tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                                  ),
                                  constraints: const BoxConstraints(),
                                  onPressed: () {
                                    // TODO: Navigate to calendar page
                                  },
                                  icon: Image.asset('assets/images/ic_calendar.png', width: 16, height: 16))
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 9,
                    ),
                    Container(
                      width: 26,
                      height: 1,
                      decoration:
                          BoxDecoration(color: MColor.skin, borderRadius: BorderRadius.circular(1), border: Border.all(width: 1, color: MColor.xFF68C2BF)),
                    ),
                    const SizedBox(
                      width: 9,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          showDatePicker(
                                  locale: const Locale('zh', 'CN'),
                                  context: context,
                                  firstDate: _tmpDateStart ?? DateTime(2020, 1, 1),
                                  lastDate: DateTime.now())
                              .then((date) {
                            if (date != null) {
                              setState(
                                () {
                                  _tmpDateEnd = date;
                                },
                              );
                            }
                          });
                        },
                        child: Container(
                          height: 32,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration:
                              BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(10), border: Border.all(color: MColor.skin, width: 1)),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _tmpDateEnd == null ? '结束日期' : df.format(_tmpDateEnd!),
                                style: const TextStyle(height: 1.4, fontSize: 14, color: MColor.xFF999999),
                              ),
                              const Spacer(),
                              IconButton(
                                  padding: EdgeInsets.zero,
                                  style: const ButtonStyle(
                                    tapTargetSize: MaterialTapTargetSize.shrinkWrap, // the '2023' part
                                  ),
                                  constraints: const BoxConstraints(),
                                  onPressed: () {
                                    // TODO: Navigate to calendar page
                                  },
                                  icon: Image.asset('assets/images/ic_calendar.png', width: 16, height: 16))
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 14,
                    ),
                  ],
                )))));
  }
}
