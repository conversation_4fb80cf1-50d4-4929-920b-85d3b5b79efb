import 'package:flutter/material.dart';
import '../constants/design.dart';

class SubmitButton extends StatelessWidget {
  final GestureTapCallback? onTap;
  final String? text;
  const SubmitButton({this.text, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient:
                LinearGradient(begin: const FractionalOffset(0.0, 0.0), end: const FractionalOffset(0.0, 1.0), colors: [Color(0xFF68C2BF), Color(0xFF4C9B93)])),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text ?? '提交',
              style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
            ),
          ],
        ),
      ),
    );
  }
}
