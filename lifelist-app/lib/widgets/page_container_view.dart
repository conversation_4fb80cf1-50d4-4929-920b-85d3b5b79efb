import 'package:flutter/material.dart';
import '../constants/design.dart';

class PageContainerView extends StatelessWidget {
  final String title;
  final String? backgroundImage;
  final List<Widget>? actions;
  final List<Widget>? footerButtons;
  final Widget? floatingActionButton;
  final bool? hideBack;
  final Widget? leading;
  final Widget body;
  const PageContainerView(
      {required this.title,
      required this.body,
      this.actions,
      this.leading,
      this.backgroundImage,
      this.footerButtons,
      this.floatingActionButton,
      this.hideBack,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        extendBody: true,
        persistentFooterButtons: footerButtons,
        floatingActionButton: floatingActionButton,
        body: SafeArea(
          top: false,
          child: Container(
            color: Colors.yellow,
            child: Stack(
              children: [
                Container(
                  color: const Color(0xFFF5F5F5),
                ),
                Builder(builder: (context) {
                  return backgroundImage?.isNotEmpty == true
                      ? Positioned(
                          // top: 0,
                          child: Image.asset(
                            this.backgroundImage!,
                            width: MediaQuery.of(context).size.width,
                            fit: BoxFit.fill,
                          ),
                        )
                      : Positioned(
                          // top: 0,
                          child: Container(
                            height: 203,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                  colors: [Color(0xFFF5F5F5), Color(0xFFAEE7CD)],
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  tileMode: TileMode.clamp),
                            ),
                          ),
                        );
                }),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: AppBar(
                        backgroundColor: Colors.transparent,
                        scrolledUnderElevation: 0,
                        centerTitle: true,
                        leading: hideBack != true
                            ? IconButton(
                                icon: Image.asset(
                                  'assets/images/ic_back.png',
                                  width: 24,
                                  height: 24,
                                ),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              )
                            : leading,
                        actions: actions,
                        titleTextStyle: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        title: Text(
                          title,
                          style: TextStyle(color: MColor.xFF1B1C1A, height: 1.4, fontSize: 16),
                        ))),
                Positioned.fill(
                    top: AppBar().preferredSize.height + MediaQuery.of(context).padding.top,
                    // top: 0,
                    child: body)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
