import 'package:flutter/material.dart';
import 'wrapped_cached_network_image.dart';

class RoundImage extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final double size;
  final Color? backgroundColor;
  const RoundImage({required this.imageUrl, required this.radius, required this.size, this.backgroundColor, super.key});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: WrappedCachedNetworkImage(
          imageUrl,
          width: size,
          height: size,
          fit: BoxFit.cover,
        ));
  }
}
