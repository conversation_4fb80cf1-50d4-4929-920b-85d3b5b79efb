import 'package:flutter/material.dart';

class ProgressView extends StatelessWidget {
  final double progress;
  final Color color;
  final double width;
  final double height;
  const ProgressView({required this.width, required this.height, required this.progress, required this.color, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
          // color: MColor.skin.withOpacity(0.2),
          borderRadius: BorderRadius.circular(height / 2),
          gradient: LinearGradient(
            // begin: Alignment.topRight,
            // end: Alignment.bottomLeft,
            stops: [0.0, progress, progress, 1.0],
            begin: const FractionalOffset(0.0, 0.0),
            end: const FractionalOffset(1.0, 0.0),
            colors: [
              color,
              color,
              color.withOpacity(0.2),
              color.withOpacity(0.2),
            ],
          )),
    );
  }
}
