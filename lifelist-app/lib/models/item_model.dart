// class AchivementItemModel {
//   final String id;
//   final String listId;
//   final String name;
//   final bool isCompleted;
//   final String? createdAt;
//   final String? updatedAt;

//   AchivementItemModel({
//     required this.id,
//     required this.listId,
//     required this.name,
//     required this.isCompleted,
//     this.createdAt,
//     this.updatedAt,
//   });

//   factory AchivementItemModel.fromJson(Map<String, dynamic> json) {
//     return AchivementItemModel(
//       id: json['id'] ?? '',
//       listId: json['list_id'] ?? '',
//       name: json['name'] ?? '',
//       isCompleted: json['is_completed'] ?? false,
//       createdAt: json['created_at'],
//       updatedAt: json['updated_at'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'list_id': listId,
//       'name': name,
//       'is_completed': isCompleted,
//       'created_at': createdAt,
//       'updated_at': updatedAt,
//     };
//   }

//   AchivementItemModel copyWith({
//     String? id,
//     String? listId,
//     String? name,
//     bool? isCompleted,
//     String? createdAt,
//     String? updatedAt,
//   }) {
//     return AchivementItemModel(
//       id: id ?? this.id,
//       listId: listId ?? this.listId,
//       name: name ?? this.name,
//       isCompleted: isCompleted ?? this.isCompleted,
//       createdAt: createdAt ?? this.createdAt,
//       updatedAt: updatedAt ?? this.updatedAt,
//     );
//   }
// }
