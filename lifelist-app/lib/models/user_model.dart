class UserModel {
  final String id;
  final String uuid;
  final String nickname;
  final String? avatar;
  final String? birthdate;
  final bool isVip;
  final String? token;
  final String? createdAt;
  final String? updatedAt;

  UserModel({
    required this.id,
    required this.uuid,
    required this.nickname,
    this.avatar,
    this.birthdate,
    required this.isVip,
    this.token,
    this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id']?.toString() ?? json['user_id']?.toString() ?? '',
      uuid: json['uuid'] ?? '',
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'],
      birthdate: json['birthdate'],
      isVip: json['is_vip'] ?? false,
      token: json['token'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uuid': uuid,
      'nickname': nickname,
      'avatar': avatar,
      'birthdate': birthdate,
      'is_vip': isVip,
      'token': token,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  UserModel copyWith({
    String? id,
    String? uuid,
    String? nickname,
    String? avatar,
    String? birthdate,
    bool? isVip,
    String? token,
    String? createdAt,
    String? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      birthdate: birthdate ?? this.birthdate,
      isVip: isVip ?? this.isVip,
      token: token ?? this.token,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
