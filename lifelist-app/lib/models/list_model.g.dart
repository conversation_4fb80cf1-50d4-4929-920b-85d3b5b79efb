// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecommendedListResp _$RecommendedListRespFromJson(Map<String, dynamic> json) =>
    RecommendedListResp(
      (json['squares'] as List<dynamic>)
          .map((e) => SquareModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RecommendedListRespToJson(
        RecommendedListResp instance) =>
    <String, dynamic>{
      'squares': instance.list,
      'pagination': instance.pagination,
    };

SquareModel _$SquareModelFromJson(Map<String, dynamic> json) => SquareModel(
      json['cover_image'] as String?,
      json['created_at'] as String,
      json['description'] as String,
      (json['id'] as num).toInt(),
      (json['original_list_id'] as num?)?.toInt(),
      json['name'] as String,
      (json['follow_count'] as num?)?.toInt(),
      json['updated_at'] as String,
      (json['author_id'] as num).toInt(),
      (json['items'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$SquareModelToJson(SquareModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'author_id': instance.authorId,
      'name': instance.name,
      'description': instance.description,
      'cover_image': instance.coverImage,
      'original_list_id': instance.originalListId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'follow_count': instance.followCount,
      'items': instance.items,
    };

AchivementModel _$AchivementModelFromJson(Map<String, dynamic> json) =>
    AchivementModel(
      (json['completed_count'] as num).toInt(),
      json['cover_image'] as String,
      json['created_at'] as String,
      json['description'] as String,
      (json['id'] as num).toInt(),
      (json['square_id'] as num?)?.toInt(),
      json['name'] as String,
      (json['progress'] as num).toDouble(),
      (json['total_count'] as num).toInt(),
      json['updated_at'] as String,
      (json['user_id'] as num).toInt(),
      (json['items'] as List<dynamic>?)
          ?.map((e) => AchivementItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AchivementModelToJson(AchivementModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'description': instance.description,
      'name': instance.name,
      'cover_image': instance.coverImage,
      'square_id': instance.squareId,
      'updated_at': instance.updatedAt,
      'created_at': instance.createdAt,
      'completed_count': instance.completedCount,
      'progress': instance.progress,
      'total_count': instance.totalCount,
      'items': instance.items,
    };

Pagination _$PaginationFromJson(Map<String, dynamic> json) => Pagination(
      json['has_next'] as bool,
      json['has_prev'] as bool,
      (json['page'] as num).toInt(),
      (json['pages'] as num).toInt(),
      (json['per_page'] as num).toInt(),
      (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$PaginationToJson(Pagination instance) =>
    <String, dynamic>{
      'has_next': instance.hasNext,
      'has_prev': instance.hasPrev,
      'page': instance.page,
      'pages': instance.pages,
      'per_page': instance.perPage,
      'total': instance.total,
    };

AchivementDetailModel _$AchivementDetailModelFromJson(
        Map<String, dynamic> json) =>
    AchivementDetailModel(
      (json['completed_count'] as num).toInt(),
      json['cover_image'] as String,
      json['created_at'] as String,
      json['description'] as String,
      (json['follow_count'] as num).toInt(),
      (json['id'] as num).toInt(),
      json['is_public'] as bool,
      json['is_shared_to_square'] as bool,
      (json['original_list_id'] as num?)?.toInt(),
      (json['items'] as List<dynamic>)
          .map((e) => AchivementItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['name'] as String,
      (json['progress'] as num).toDouble(),
      (json['total_count'] as num).toInt(),
      json['updated_at'] as String,
      (json['user_id'] as num).toInt(),
    );

Map<String, dynamic> _$AchivementDetailModelToJson(
        AchivementDetailModel instance) =>
    <String, dynamic>{
      'completed_count': instance.completedCount,
      'cover_image': instance.coverImage,
      'created_at': instance.createdAt,
      'description': instance.description,
      'follow_count': instance.followCount,
      'id': instance.id,
      'is_public': instance.isPublic,
      'is_shared_to_square': instance.isSharedToSquare,
      'original_list_id': instance.originalListId,
      'items': instance.items,
      'name': instance.name,
      'progress': instance.progress,
      'total_count': instance.totalCount,
      'updated_at': instance.updatedAt,
      'user_id': instance.userId,
    };

AchivementItemModel _$AchivementItemModelFromJson(Map<String, dynamic> json) =>
    AchivementItemModel(
      json['created_at'] as String,
      (json['id'] as num).toInt(),
      json['is_completed'] as bool,
      (json['list_id'] as num).toInt(),
      json['name'] as String,
      (json['sort_order'] as num).toInt(),
      json['updated_at'] as String,
    );

Map<String, dynamic> _$AchivementItemModelToJson(
        AchivementItemModel instance) =>
    <String, dynamic>{
      'created_at': instance.createdAt,
      'id': instance.id,
      'is_completed': instance.isCompleted,
      'list_id': instance.listId,
      'name': instance.name,
      'sort_order': instance.sortOrder,
      'updated_at': instance.updatedAt,
    };
