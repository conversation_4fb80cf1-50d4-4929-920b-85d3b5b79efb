class AgeInfo {
  final int years;
  final int days;
  final int hours;
  final int minutes;
  final int seconds;
  final int totalDaysLived;
  final String formattedAge;
  final double preciseAge;

  AgeInfo({
    required this.years,
    required this.days,
    required this.hours,
    required this.minutes,
    required this.seconds,
    required this.totalDaysLived,
    required this.formattedAge,
    required this.preciseAge,
  });

  factory AgeInfo.fromJson(Map<String, dynamic> json) {
    return AgeInfo(
      years: json['years'] ?? 0,
      days: json['days'] ?? 0,
      hours: json['hours'] ?? 0,
      minutes: json['minutes'] ?? 0,
      seconds: json['seconds'] ?? 0,
      totalDaysLived: json['total_days_lived'] ?? 0,
      formattedAge: json['formatted_age'] ?? '0',
      preciseAge: (json['precise_age'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'years': years,
      'days': days,
      'hours': hours,
      'minutes': minutes,
      'seconds': seconds,
      'total_days_lived': totalDaysLived,
      'formatted_age': formattedAge,
      'precise_age': preciseAge,
    };
  }
}

class LifeEvent {
  final String id;
  final String icon;
  final String title;
  final String description;

  LifeEvent({
    required this.id,
    required this.icon,
    required this.title,
    required this.description,
  });

  factory LifeEvent.fromJson(Map<String, dynamic> json) {
    return LifeEvent(
      id: json['id'] ?? '',
      icon: json['icon'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'icon': icon,
      'title': title,
      'description': description,
    };
  }
}

class RemainingModel {
  final AgeInfo ageInfo;
  final List<LifeEvent> lifeEvents;
  final List<LifeEvent> allEvents;

  RemainingModel({
    required this.ageInfo,
    required this.lifeEvents,
    required this.allEvents,
  });

  factory RemainingModel.fromJson(Map<String, dynamic> json) {
    return RemainingModel(
      ageInfo: AgeInfo.fromJson(json['age_info'] ?? {}),
      lifeEvents: (json['life_events'] as List<dynamic>? ?? [])
          .map((e) => LifeEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      allEvents: (json['all_events'] as List<dynamic>? ?? [])
          .map((e) => LifeEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'age_info': ageInfo.toJson(),
      'life_events': lifeEvents.map((e) => e.toJson()).toList(),
      'all_events': allEvents.map((e) => e.toJson()).toList(),
    };
  }
}
