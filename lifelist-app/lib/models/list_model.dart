import 'package:json_annotation/json_annotation.dart';
import 'package:lifelist/common/url_helper.dart';

part 'list_model.g.dart';

@JsonSerializable()
class RecommendedListResp extends Object {
  @JsonKey(name: 'squares')
  List<SquareModel> list;

  @Json<PERSON>ey(name: 'pagination')
  Pagination pagination;

  RecommendedListResp(
    this.list,
    this.pagination,
  );

  factory RecommendedListResp.fromJson(Map<String, dynamic> srcJson) => _$RecommendedListRespFromJson(srcJson);

  Map<String, dynamic> toJson() => _$RecommendedListRespToJson(this);
}

@JsonSerializable()
class SquareModel extends Object {
  @Json<PERSON>ey(name: 'id')
  int id;

  @Json<PERSON>ey(name: 'author_id')
  int authorId;

  @Json<PERSON>ey(name: 'name')
  String name;

  @Json<PERSON>ey(name: 'description')
  String description;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'cover_image')
  String? coverImage;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'original_list_id')
  int? originalListId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  String createdAt;

  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  String updatedAt;

  @JsonKey(name: 'follow_count')
  int? followCount;

  @JsonKey(name: 'items')
  List<String>? items;

  SquareModel(
      this.coverImage, this.createdAt, this.description, this.id, this.originalListId, this.name, this.followCount, this.updatedAt, this.authorId, this.items);

  factory SquareModel.fromJson(Map<String, dynamic> srcJson) => _$SquareModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$SquareModelToJson(this);

  String get getCoverImage {
    return UrlHelper.getFullImageUrl(coverImage);
  }
}

@JsonSerializable()
class AchivementModel extends Object {
  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'user_id')
  int userId;

  @JsonKey(name: 'description')
  String description;

  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'cover_image')
  String coverImage;

  @JsonKey(name: 'square_id')
  int? squareId;

  @JsonKey(name: 'updated_at')
  String updatedAt;

  @JsonKey(name: 'created_at')
  String createdAt;

  @JsonKey(name: 'completed_count')
  int completedCount;

  @JsonKey(name: 'progress')
  double progress;

  @JsonKey(name: 'total_count')
  int totalCount;

  @JsonKey(name: 'items')
  List<AchivementItemModel>? items;

  AchivementModel(this.completedCount, this.coverImage, this.createdAt, this.description, this.id, this.squareId, this.name, this.progress, this.totalCount,
      this.updatedAt, this.userId, this.items);

  factory AchivementModel.fromJson(Map<String, dynamic> srcJson) => _$AchivementModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AchivementModelToJson(this);

  double get getProgress {
    if (totalCount == 0) return 0.0;
    return completedCount / totalCount;
  }

  String get getCoverImage {
    return UrlHelper.getFullImageUrl(coverImage);
  }

  String get progressText {
    return '$completedCount/$totalCount';
  }
}

@JsonSerializable()
class Pagination extends Object {
  @JsonKey(name: 'has_next')
  bool hasNext;

  @JsonKey(name: 'has_prev')
  bool hasPrev;

  @JsonKey(name: 'page')
  int page;

  @JsonKey(name: 'pages')
  int pages;

  @JsonKey(name: 'per_page')
  int perPage;

  @JsonKey(name: 'total')
  int total;

  Pagination(
    this.hasNext,
    this.hasPrev,
    this.page,
    this.pages,
    this.perPage,
    this.total,
  );

  factory Pagination.fromJson(Map<String, dynamic> srcJson) => _$PaginationFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PaginationToJson(this);
}

@JsonSerializable()
class AchivementDetailModel extends Object {
  @JsonKey(name: 'completed_count')
  int completedCount;

  @JsonKey(name: 'cover_image')
  String coverImage;

  @JsonKey(name: 'created_at')
  String createdAt;

  @JsonKey(name: 'description')
  String description;

  @JsonKey(name: 'follow_count')
  int followCount;

  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'is_public')
  bool isPublic;

  @JsonKey(name: 'is_shared_to_square')
  bool isSharedToSquare;

  @JsonKey(name: 'original_list_id')
  int? originalListId;

  @JsonKey(name: 'items')
  List<AchivementItemModel> items;

  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'progress')
  double progress;

  @JsonKey(name: 'total_count')
  int totalCount;

  @JsonKey(name: 'updated_at')
  String updatedAt;

  @JsonKey(name: 'user_id')
  int userId;

  AchivementDetailModel(
    this.completedCount,
    this.coverImage,
    this.createdAt,
    this.description,
    this.followCount,
    this.id,
    this.isPublic,
    this.isSharedToSquare,
    this.originalListId,
    this.items,
    this.name,
    this.progress,
    this.totalCount,
    this.updatedAt,
    this.userId,
  );

  String get getCoverImage {
    return UrlHelper.getFullImageUrl(coverImage);
  }

  factory AchivementDetailModel.fromJson(Map<String, dynamic> srcJson) => _$AchivementDetailModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AchivementDetailModelToJson(this);
}

@JsonSerializable()
class AchivementItemModel extends Object {
  @JsonKey(name: 'created_at')
  String createdAt;

  @JsonKey(name: 'id')
  int id;

  @JsonKey(name: 'is_completed')
  bool isCompleted;

  @JsonKey(name: 'list_id')
  int listId;

  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'sort_order')
  int sortOrder;

  @JsonKey(name: 'updated_at')
  String updatedAt;

  AchivementItemModel(
    this.createdAt,
    this.id,
    this.isCompleted,
    this.listId,
    this.name,
    this.sortOrder,
    this.updatedAt,
  );

  factory AchivementItemModel.fromJson(Map<String, dynamic> srcJson) => _$AchivementItemModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AchivementItemModelToJson(this);
}
