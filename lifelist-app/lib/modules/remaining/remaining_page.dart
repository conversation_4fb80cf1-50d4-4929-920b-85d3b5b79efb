import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../constants/design.dart';
import '../../models/remaining_model.dart';
import '../../services/remaining_service.dart';
import '../../widgets/page_container_view.dart';
import '../../tools/tools.dart';

class RemainingPage extends StatefulWidget {
  const RemainingPage({Key? key}) : super(key: key);

  @override
  State<RemainingPage> createState() => _RemainingPageState();
}

class _RemainingPageState extends State<RemainingPage> with TickerProviderStateMixin {
  RemainingModel? _remainingData;
  bool _isLoading = true;
  String? _errorMessage;
  Timer? _clockTimer;
  late AnimationController _clockAnimationController;
  late AnimationController _refreshAnimationController;

  @override
  void initState() {
    super.initState();
    _clockAnimationController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();

    _refreshAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _loadRemainingData();
    _startClockTimer();
  }

  @override
  void dispose() {
    _clockTimer?.cancel();
    _clockAnimationController.dispose();
    _refreshAnimationController.dispose();
    super.dispose();
  }

  void _startClockTimer() {
    _clockTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && _remainingData != null) {
        setState(() {
          // 更新秒数
          final ageInfo = _remainingData!.ageInfo;
          final newSeconds = (ageInfo.seconds + 1) % 60;
          final newMinutes = newSeconds == 0 ? (ageInfo.minutes + 1) % 60 : ageInfo.minutes;
          final newHours = newMinutes == 0 && newSeconds == 0 ? (ageInfo.hours + 1) % 24 : ageInfo.hours;

          _remainingData = RemainingModel(
            ageInfo: AgeInfo(
              years: ageInfo.years,
              days: ageInfo.days,
              hours: newHours,
              minutes: newMinutes,
              seconds: newSeconds,
              totalDaysLived: ageInfo.totalDaysLived,
              formattedAge: ageInfo.formattedAge,
              preciseAge: ageInfo.preciseAge,
            ),
            lifeEvents: _remainingData!.lifeEvents,
            allEvents: _remainingData!.allEvents,
          );
        });
      }
    });
  }

  Future<void> _loadRemainingData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await RemainingService().getRemainingLife();

      if (response.code == 1 && response.data != null) {
        setState(() {
          _remainingData = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? '获取数据失败';
          _isLoading = false;
        });

        // 如果是生日未设置的错误，显示设置生日对话框
        if (response.message?.contains('生日') == true) {
          _showBirthdateDialog();
        }
      }
    } catch (e) {
      logger.e('加载余生数据失败: $e');
      setState(() {
        _errorMessage = '网络错误，请稍后重试';
        _isLoading = false;
      });
    }
  }

  void _showBirthdateDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('设置生日'),
        content: const Text('请先设置您的生日信息，以便计算余生时钟。'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.pop();
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.push('/profile').then((_) {
                _loadRemainingData();
              });
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  void _refreshEvents() {
    if (_remainingData != null && _remainingData!.allEvents.isNotEmpty) {
      _refreshAnimationController.forward().then((_) {
        _refreshAnimationController.reset();
      });

      setState(() {
        // 从所有事件中随机选择4个
        final allEvents = List<LifeEvent>.from(_remainingData!.allEvents);
        allEvents.shuffle();
        final newEvents = allEvents.take(4).toList();

        _remainingData = RemainingModel(
          ageInfo: _remainingData!.ageInfo,
          lifeEvents: newEvents,
          allEvents: _remainingData!.allEvents,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '余生时钟',
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorView()
              : _buildContent(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: MColor.xFF999999,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? '加载失败',
              style: const TextStyle(
                fontSize: 16,
                color: MColor.xFF777777,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadRemainingData,
              style: ElevatedButton.styleFrom(
                backgroundColor: MColor.skin,
                foregroundColor: Colors.white,
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_remainingData == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 时钟区域
          _buildClockSection(),

          const SizedBox(height: 32),

          // 年龄显示
          _buildAgeSection(),

          const SizedBox(height: 32),

          // 余生可以标题
          _buildSectionTitle(),

          const SizedBox(height: 16),

          // 事件列表
          _buildEventsList(),

          const SizedBox(height: 32),

          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildClockSection() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: MColor.xFFF5F5F5,
        border: Border.all(color: MColor.xFF333333, width: 3),
      ),
      child: AnimatedBuilder(
        animation: _clockAnimationController,
        builder: (context, child) {
          return CustomPaint(
            painter: ClockPainter(
              hours: _remainingData!.ageInfo.hours,
              minutes: _remainingData!.ageInfo.minutes,
              seconds: _remainingData!.ageInfo.seconds,
            ),
            child: Container(),
          );
        },
      ),
    );
  }

  Widget _buildAgeSection() {
    final ageInfo = _remainingData!.ageInfo;
    return Column(
      children: [
        RichText(
          text: TextSpan(
            style: const TextStyle(
              fontSize: 24,
              color: MColor.xFF333333,
            ),
            children: [
              const TextSpan(text: '你 '),
              TextSpan(
                text: ageInfo.formattedAge,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: MColor.skin,
                ),
              ),
              const TextSpan(text: ' 岁了'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle() {
    return const Text(
      '余生还可以',
      style: TextStyle(
        fontSize: 18,
        color: MColor.xFF777777,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _buildEventsList() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _remainingData!.lifeEvents.length,
      itemBuilder: (context, index) {
        final event = _remainingData!.lifeEvents[index];
        return _buildEventCard(event);
      },
    );
  }

  Widget _buildEventCard(LifeEvent event) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            event.icon,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(height: 8),
          Text(
            event.title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: MColor.xFF333333,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              // TODO: 实现分享功能
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: MColor.skin),
              foregroundColor: MColor.skin,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('分享'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              // TODO: 实现保存功能
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: MColor.skin,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('保存'),
          ),
        ),
      ],
    );
  }
}

class ClockPainter extends CustomPainter {
  final int hours;
  final int minutes;
  final int seconds;

  ClockPainter({
    required this.hours,
    required this.minutes,
    required this.seconds,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 20;

    // 绘制时钟刻度
    _drawClockMarks(canvas, center, radius);

    // 绘制指针
    _drawHands(canvas, center, radius);

    // 绘制中心点
    final centerPaint = Paint()
      ..color = MColor.xFF333333
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 6, centerPaint);
  }

  void _drawClockMarks(Canvas canvas, Offset center, double radius) {
    final markPaint = Paint()
      ..color = MColor.xFF999999
      ..strokeWidth = 2;

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30) * math.pi / 180;
      final startRadius = radius - 15;
      final endRadius = radius - 5;

      final start = Offset(
        center.dx + startRadius * math.cos(angle - math.pi / 2),
        center.dy + startRadius * math.sin(angle - math.pi / 2),
      );
      final end = Offset(
        center.dx + endRadius * math.cos(angle - math.pi / 2),
        center.dy + endRadius * math.sin(angle - math.pi / 2),
      );

      canvas.drawLine(start, end, markPaint);
    }
  }

  void _drawHands(Canvas canvas, Offset center, double radius) {
    // 时针
    final hourAngle = ((hours % 12) * 30 + minutes * 0.5) * math.pi / 180;
    _drawHand(canvas, center, hourAngle, radius * 0.5, 4, MColor.xFF333333);

    // 分针
    final minuteAngle = (minutes * 6 + seconds * 0.1) * math.pi / 180;
    _drawHand(canvas, center, minuteAngle, radius * 0.7, 3, MColor.xFF333333);

    // 秒针
    final secondAngle = seconds * 6 * math.pi / 180;
    _drawHand(canvas, center, secondAngle, radius * 0.8, 1, MColor.xFFD05363);
  }

  void _drawHand(Canvas canvas, Offset center, double angle, double length, double strokeWidth, Color color) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final end = Offset(
      center.dx + length * math.cos(angle - math.pi / 2),
      center.dy + length * math.sin(angle - math.pi / 2),
    );

    canvas.drawLine(center, end, paint);
  }

  @override
  bool shouldRepaint(ClockPainter oldDelegate) {
    return oldDelegate.hours != hours || oldDelegate.minutes != minutes || oldDelegate.seconds != seconds;
  }
}
