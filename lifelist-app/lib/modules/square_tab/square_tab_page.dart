import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/common/app_state_manager.dart';
import 'package:lifelist/common/user_manager.dart';
import 'package:lifelist/modules/home/<USER>';
import 'package:lifelist/modules/home/<USER>';
import 'package:lifelist/router/router.dart';
import 'package:lifelist/services/achivement_service.dart';
import 'package:lifelist/services/square_service.dart';
import 'package:lifelist/widgets/empty_view.dart';
import '../../constants/design.dart';
import '../../widgets/page_container_view.dart';
import '../../services/api_service.dart';
import '../../models/list_model.dart';
import '../../tools/tools.dart';
import 'bloc/square_bloc.dart';
import 'bloc/square_event.dart';
import 'bloc/square_state.dart';

class SquareTabPage extends StatelessWidget {
  const SquareTabPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SquareBloc()..add(const LoadSquareData()),
      child: const _SquareTabView(),
    );
  }
}

class _SquareTabView extends StatefulWidget {
  const _SquareTabView();

  @override
  State<_SquareTabView> createState() => _SquareTabViewState();
}

class _SquareTabViewState extends State<_SquareTabView> {
  void _onTabActivated() {
    // 当切换到广场Tab时刷新数据
    context.read<SquareBloc>().add(const RefreshSquareData());
  }

  void _onTabbarChanged() {
    if (mounted) {
      if (TabbarStateManager.instance.currentIndex == 0) {
        _onTabActivated();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 监听登录状态变化
    TabbarStateManager.instance.addListener(_onTabbarChanged);
  }

  @override
  void dispose() {
    TabbarStateManager.instance.removeListener(_onTabbarChanged);
    super.dispose();
  }

  void _followList(int squareId) {
    if (!AppStateManager.instance.isLoggedIn) {
      context.push('/login').then((_) {
        if (AppStateManager.instance.isLoggedIn) {
          _followList(squareId);
        }
      });
      return;
    }
    context.read<SquareBloc>().add(FollowSquare(squareId));
  }

  void _goRemaining() {
    if (!AppStateManager.instance.isLoggedIn) {
      context.push('/login').then((_) {
        if (AppStateManager.instance.isLoggedIn) {
          _goRemaining();
        }
      });
      return;
    }
    context.push(Routes.remainingPath);
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '美瞬人生清单',
      hideBack: true,
      leading: IconButton(
        icon: Icon(
          Icons.alarm,
          size: 20,
          color: MColor.xFFD05363,
        ),
        onPressed: () {
          _goRemaining();
        },
      ),
      body: BlocBuilder<SquareBloc, SquareState>(
        builder: (context, state) {
          if (state is SquareLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is SquareError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<SquareBloc>().add(const LoadSquareData());
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          } else if (state is SquareLoaded || state is SquareFollowing || state is SquareFollowSuccess || state is SquareFollowError) {
            List<SquareModel> squares = [];
            if (state is SquareLoaded)
              squares = state.squares;
            else if (state is SquareFollowing)
              squares = state.squares;
            else if (state is SquareFollowSuccess)
              squares = state.squares;
            else if (state is SquareFollowError) squares = state.squares;

            return RefreshIndicator(
              triggerMode: RefreshIndicatorTriggerMode.anywhere,
              onRefresh: () async {
                context.read<SquareBloc>().add(const RefreshSquareData());
              },
              child: squares.isEmpty
                  ? const EmptyView()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: squares.length,
                      itemBuilder: (context, index) {
                        final square = squares[index];
                        final isFollowing = state is SquareFollowing && state.followingSquareId == square.id;
                        return _buildListCard(square);
                      },
                    ),
            );
          }

          return const EmptyView();
        },
      ),
    );
  }

  Widget _buildListCard(SquareModel list) {
    return GestureDetector(
      onTap: () {
        context.push('${Routes.squareDetailPath}/${list.id}');
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图
            Container(
              height: 160,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                color: MColor.xFFECECEC,
              ),
              child: list.getCoverImage.isNotEmpty
                  ? ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                      child: Image.network(
                        list.getCoverImage,
                        width: double.infinity,
                        height: 160,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: double.infinity,
                            height: 160,
                            color: MColor.xFFECECEC,
                            child: const Icon(
                              Icons.image,
                              size: 48,
                              color: MColor.xFF999999,
                            ),
                          );
                        },
                      ),
                    )
                  : Container(
                      width: double.infinity,
                      height: 160,
                      color: MColor.xFFECECEC,
                      child: const Icon(
                        Icons.list_alt,
                        size: 48,
                        color: MColor.xFF999999,
                      ),
                    ),
            ),

            // 内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    list.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: MColor.xFF333333,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // 描述
                  if (list.description.isNotEmpty)
                    Text(
                      list.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: MColor.xFF777777,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const SizedBox(height: 12),

                  // 统计信息
                  Row(
                    children: [
                      const Icon(
                        Icons.favorite,
                        size: 16,
                        color: MColor.xFFD05363,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${list.followCount ?? 0}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: MColor.xFF777777,
                        ),
                      ),

                      const Spacer(),

                      // Follow按钮
                      if ('${list.authorId}' == UserManager.instance.currentUser?.id) ...{
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: MColor.xFF777777,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Text(
                            '我的分享',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        )
                      } else ...{
                        GestureDetector(
                          onTap: () => _followList(list.id),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: MColor.skin,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Text(
                              '关注',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      }
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
