import 'package:equatable/equatable.dart';

abstract class SquareEvent extends Equatable {
  const SquareEvent();

  @override
  List<Object> get props => [];
}

class LoadSquareData extends SquareEvent {
  const LoadSquareData();
}

class RefreshSquareData extends SquareEvent {
  const RefreshSquareData();
}

class FollowSquare extends SquareEvent {
  final int squareId;

  const FollowSquare(this.squareId);

  @override
  List<Object> get props => [squareId];
}
