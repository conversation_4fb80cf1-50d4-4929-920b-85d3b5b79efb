import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lifelist/services/square_service.dart';
import 'package:lifelist/common/toast_utils.dart';
import 'package:lifelist/tools/tools.dart';
import 'square_event.dart';
import 'square_state.dart';

class SquareBloc extends Bloc<SquareEvent, SquareState> {
  final SquareService _squareService;

  SquareBloc({SquareService? squareService})
      : _squareService = squareService ?? SquareService(),
        super(const SquareInitial()) {
    on<LoadSquareData>(_onLoadSquareData);
    on<RefreshSquareData>(_onRefreshSquareData);
    on<FollowSquare>(_onFollowSquare);
  }

  Future<void> _onLoadSquareData(
    LoadSquareData event,
    Emitter<SquareState> emit,
  ) async {
    emit(const SquareLoading());
    try {
      final response = await _squareService.getRecommendedLists();
      if (response.code == 1 && response.data != null) {
        emit(SquareLoaded(response.data!.list));
      } else {
        emit(SquareError(response.msg ?? '加载失败'));
      }
    } catch (e) {
      emit(SquareError('加载失败: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshSquareData(
    RefreshSquareData event,
    Emitter<SquareState> emit,
  ) async {
    try {
      final response = await _squareService.getRecommendedLists();
      if (response.code == 1 && response.data != null) {
        emit(SquareLoaded(response.data!.list));
      } else {
        emit(SquareError(response.msg ?? '刷新失败'));
      }
    } catch (e) {
      emit(SquareError('刷新失败: ${e.toString()}'));
    }
  }

  Future<void> _onFollowSquare(
    FollowSquare event,
    Emitter<SquareState> emit,
  ) async {
    final currentState = state;
    if (currentState is SquareLoaded) {
      emit(SquareFollowing(currentState.squares, event.squareId));

      try {
        final response = await _squareService.followList(event.squareId);
        if (response.code == 1) {
          showToast(response.msg ?? '关注成功');
          emit(SquareFollowSuccess(currentState.squares, response.msg ?? '关注成功'));

          // 刷新数据
          add(const RefreshSquareData());
        } else {
          showToast(response.msg ?? '关注失败');
          emit(SquareFollowError(currentState.squares, response.msg ?? '关注失败'));
        }
      } catch (e) {
        final errorMessage = '关注失败: ${e.toString()}';
        showToast(errorMessage);
        emit(SquareFollowError(currentState.squares, errorMessage));
      }
    }
  }
}
