import 'package:equatable/equatable.dart';
import 'package:lifelist/models/list_model.dart';

abstract class SquareState extends Equatable {
  const SquareState();

  @override
  List<Object> get props => [];
}

class <PERSON>Initial extends SquareState {
  const SquareInitial();
}

class SquareLoading extends SquareState {
  const SquareLoading();
}

class SquareLoaded extends SquareState {
  final List<SquareModel> squares;

  const SquareLoaded(this.squares);

  @override
  List<Object> get props => [squares];
}

class SquareError extends SquareState {
  final String message;

  const SquareError(this.message);

  @override
  List<Object> get props => [message];
}

class SquareFollowing extends SquareState {
  final List<SquareModel> squares;
  final int followingSquareId;

  const SquareFollowing(this.squares, this.followingSquareId);

  @override
  List<Object> get props => [squares, followingSquareId];
}

class SquareFollowSuccess extends SquareState {
  final List<SquareModel> squares;
  final String message;

  const SquareFollowSuccess(this.squares, this.message);

  @override
  List<Object> get props => [squares, message];
}

class SquareFollowError extends SquareState {
  final List<SquareModel> squares;
  final String message;

  const SquareFollowError(this.squares, this.message);

  @override
  List<Object> get props => [squares, message];
}
