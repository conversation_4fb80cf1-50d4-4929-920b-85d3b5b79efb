import 'package:flutter/material.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/services/achivement_service.dart';
import 'package:lifelist/services/square_service.dart';
import 'package:lifelist/widgets/page_container_view.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/tools/tools.dart';
import 'package:lifelist/common/url_helper.dart';

class SquareDetailPage extends StatefulWidget {
  final String listId;

  const SquareDetailPage({
    Key? key,
    required this.listId,
  }) : super(key: key);

  @override
  State<SquareDetailPage> createState() => _SquareDetailPageState();
}

class _SquareDetailPageState extends State<SquareDetailPage> {
  SquareModel? _data;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadListDetail();
  }

  Future<void> _loadListDetail() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await SquareService().getSquareDetail(widget.listId);
      if (response.code == 1 && response.data != null) {
        setState(() {
          _data = response.data;
          _isLoading = false;
        });
      } else {
        showToast(response.msg ?? '获取清单详情失败');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      showToast('网络错误: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: _data?.name ?? '清单详情',
      actions: [],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _data == null
              ? const Center(child: Text('清单不存在'))
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    final list = _data!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 背景图
          _buildCoverImage(list),
          const SizedBox(height: 16),

          // 描述
          _buildDescription(list),
          const SizedBox(height: 16),

          // 子项列表
          _buildItemsList(list),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCoverImage(SquareModel list) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: MColor.skin.withOpacity(0.1),
      ),
      child: list.getCoverImage.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                list.getCoverImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
              ),
            )
          : _buildPlaceholderImage(),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            MColor.skin.withOpacity(0.3),
            MColor.skin.withOpacity(0.1),
          ],
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.image,
          size: 48,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildDescription(SquareModel list) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          list.description ?? '暂无描述',
          style: const TextStyle(
            fontSize: 14,
            color: MColor.xFF777777,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildItemsList(SquareModel list) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '子项列表',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 12),
        if (list.items?.isEmpty == true)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                SizedBox(height: 8),
                Text(
                  '暂无子项',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          )
        else
          ...list.items!.map((item) => _buildItemCard(item)).toList(),
      ],
    );
  }

  Widget _buildItemCard(String item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.radio_button_unchecked,
                  color: Colors.grey[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
