import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/common/loading.dart';
import 'package:lifelist/common/user_manager.dart';
import 'package:lifelist/common/app_state_manager.dart';
import 'package:lifelist/services/user_service.dart';
import '../../constants/design.dart';
import '../../widgets/page_container_view.dart';
import '../../models/user_model.dart';
import '../../tools/tools.dart';
import '../../router/router.dart';

class MineTabPage extends StatefulWidget {
  const MineTabPage({super.key});

  @override
  State<MineTabPage> createState() => _MineTabPageState();
}

class _MineTabPageState extends State<MineTabPage> with AutomaticKeepAliveClientMixin {
  UserModel? _user;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();

    // 监听登录状态变化
    AppStateManager.instance.addListener(_onLoginStateChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面重新获得焦点时刷新数据
    _loadUserInfo();
  }

  @override
  void dispose() {
    AppStateManager.instance.removeListener(_onLoginStateChanged);
    super.dispose();
  }

  void _onLoginStateChanged() {
    if (mounted) {
      setState(() {
        _user = AppStateManager.instance.currentUser;
      });
    }
  }

  Future<void> _loadUserInfo() async {
    // 首先从本地加载用户信息
    final localUser = UserManager.instance.currentUser;
    if (localUser != null) {
      setState(() {
        _user = localUser;
      });
    }

    // 如果用户已登录，尝试从服务器更新用户信息
    if (UserManager.instance.isLoggedIn) {
      try {
        final userResponse = await UserService().getUserProfile();
        setState(() {
          if (userResponse.code == 1 && userResponse.data != null) {
            _user = userResponse.data;
          }
        });

        // 更新本地用户信息
        if (userResponse.code == 1 && userResponse.data != null) {
          await UserManager.instance.updateUser(userResponse.data!);
        }
      } catch (e) {
        logger.i('加载用户信息失败: $e');
      }
    }
  }

  void _openProfile() {
    context.push('/profile').then((result) {
      if (result == true) {
        // 如果个人资料有更新，重新加载用户信息
        _loadUserInfo();
      }
    });
  }

  void _openLoginPage() {
    context.push('/login').then((_) {
      // 登录页面返回后，重新加载用户信息
      _loadUserInfo();
    });
  }

  Future<void> _logout() async {
    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？这将清除本地保存的用户数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text(
              '确定',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await UserManager.instance.clearUser();
      setState(() {
        _user = null;
      });
      showToast('已退出登录');
    }
  }

  Future<void> _deleteAccount() async {
    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('注销账户'),
        content: const Text('确定要注销账户吗？\n\n注意：此操作将永久删除您的账户和所有数据，包括：\n• 所有清单和清单项\n• 个人资料信息\n• 关注记录\n\n此操作不可恢复！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text(
              '确定注销',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      Loading.show();
      try {
        final response = await UserService().deleteAccount();

        if (response.code == 1) {
          // 清除本地用户信息
          await UserManager.instance.clearUser();
          setState(() {
            _user = null;
          });
          showToast('账户注销成功');
        } else {
          showToast(response.msg ?? '注销失败');
        }
      } catch (e) {
        showToast('网络错误: $e');
      } finally {
        Loading.dismiss();
      }
    }
  }

  void _openPrivacyPolicy() {
    context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/privacy', 'title': '隐私政策'});
  }

  void _openUserAgreement() {
    context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/useragreement', 'title': '用户协议'});
  }

  void _shareApp() {
    showToast('分享功能开发中');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，因为使用了AutomaticKeepAliveClientMixin
    return PageContainerView(
      title: '我的',
      hideBack: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 用户信息卡片
            _buildUserCard(),

            const SizedBox(height: 24),

            // 功能列表
            _buildFunctionList(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserCard() {
    return GestureDetector(
      onTap: () {
        final isLoggedIn = UserManager.instance.isLoggedIn;
        if (!isLoggedIn) {
          _openLoginPage();
        } else {
          _openProfile();
        }
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 头像
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: MColor.xFFECECEC,
              ),
              child: _user?.avatar?.isNotEmpty == true
                  ? ClipOval(
                      child: Image.network(
                        _user!.avatar!,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'assets/images/icon_white.png',
                            width: 40,
                            height: 40,
                            fit: BoxFit.scaleDown,
                          );
                        },
                      ),
                    )
                  : Image.asset(
                      'assets/images/icon_white.png',
                      width: 40,
                      height: 40,
                      fit: BoxFit.scaleDown,
                    ),
            ),

            const SizedBox(height: 16),

            // 昵称
            Text(
              _user?.nickname ?? '未登录',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: MColor.xFF333333,
              ),
            ),

            const SizedBox(height: 8),

            // // VIP状态
            // Container(
            //   padding: const EdgeInsets.symmetric(
            //     horizontal: 12,
            //     vertical: 6,
            //   ),
            //   decoration: BoxDecoration(
            //     color: _user?.isVip == true ? MColor.xFFFFD180 : MColor.xFFECECEC,
            //     borderRadius: BorderRadius.circular(12),
            //   ),
            //   child: Text(
            //     _user?.isVip == true ? 'VIP用户' : '免费用户',
            //     style: TextStyle(
            //       fontSize: 12,
            //       color: _user?.isVip == true ? MColor.xFF553600 : MColor.xFF777777,
            //       fontWeight: FontWeight.w500,
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionList() {
    final isLoggedIn = UserManager.instance.isLoggedIn;

    final functions = [
      if (isLoggedIn)
        {
          'icon': Icons.person,
          'title': '个人资料',
          'subtitle': '编辑头像和昵称',
          'onTap': _openProfile,
        },
      {
        'icon': Icons.privacy_tip,
        'title': '隐私政策',
        'subtitle': '了解我们如何保护您的隐私',
        'onTap': _openPrivacyPolicy,
      },
      {
        'icon': Icons.description,
        'title': '用户协议',
        'subtitle': '查看服务条款',
        'onTap': _openUserAgreement,
      },
      if (!isLoggedIn)
        {
          'icon': Icons.login,
          'title': '登录',
          'subtitle': '登录以同步数据',
          'onTap': _openLoginPage,
        },
      if (isLoggedIn)
        {
          'icon': Icons.logout,
          'title': '退出登录',
          'subtitle': '清除本地数据',
          'onTap': _logout,
        },
      if (isLoggedIn)
        {
          'icon': Icons.delete_forever,
          'title': '注销账户',
          'subtitle': '永久删除账户和所有数据',
          'onTap': _deleteAccount,
        },
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: functions.asMap().entries.map((entry) {
          final index = entry.key;
          final function = entry.value;
          final isLast = index == functions.length - 1;

          return Column(
            children: [
              ListTile(
                leading: Icon(
                  function['icon'] as IconData,
                  color: MColor.skin,
                  size: 24,
                ),
                title: Text(
                  function['title'] as String,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: MColor.xFF333333,
                  ),
                ),
                subtitle: Text(
                  function['subtitle'] as String,
                  style: const TextStyle(
                    fontSize: 14,
                    color: MColor.xFF777777,
                  ),
                ),
                trailing: const Icon(
                  Icons.chevron_right,
                  color: MColor.xFF999999,
                ),
                onTap: function['onTap'] as VoidCallback,
              ),
              if (!isLast)
                const Divider(
                  height: 1,
                  indent: 56,
                  color: MColor.xFFECECEC,
                ),
            ],
          );
        }).toList(),
      ),
    );
  }
}
