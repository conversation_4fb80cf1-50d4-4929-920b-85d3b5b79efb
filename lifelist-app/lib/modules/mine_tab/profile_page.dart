import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/common/image_picker_helper.dart';
import 'package:lifelist/common/user_manager.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/models/user_model.dart';
import 'package:lifelist/services/user_service.dart';
import 'package:lifelist/tools/tools.dart';
import 'package:lifelist/widgets/page_container_view.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nicknameController;
  late TextEditingController _birthdateController;

  UserModel? _user;
  String? _avatarUrl;
  bool _isLoading = false;
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _user = UserManager.instance.currentUser;
    _nicknameController = TextEditingController(text: _user?.nickname ?? '');
    _birthdateController = TextEditingController(text: _user?.birthdate ?? '');
    _avatarUrl = _user?.avatar;

    if (_user?.birthdate != null && _user!.birthdate!.isNotEmpty) {
      try {
        _selectedDate = DateTime.parse(_user!.birthdate!);
      } catch (e) {
        print('解析生日失败: $e');
      }
    }
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _birthdateController.dispose();
    super.dispose();
  }

  Future<void> _selectAvatar() async {
    final imageUrl = await ImagePickerHelper.showImagePickerDialog(context);
    if (imageUrl != null) {
      setState(() {
        _avatarUrl = imageUrl;
      });
    }
  }

  Future<void> _selectBirthdate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('zh', 'CN'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _birthdateController.text = picked.toString().split(' ')[0];
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await UserService().updateUserProfile(
        nickname: _nicknameController.text.trim(),
        avatar: _avatarUrl,
        birthdate: _birthdateController.text.trim(),
      );

      if (response.code == 1 && response.data != null) {
        // 更新本地用户信息
        await UserManager.instance.updateUser(response.data!);

        showToast('资料更新成功');

        if (mounted) {
          context.pop(true); // 返回true表示有更新
        }
      } else {
        showToast(response.msg ?? '更新失败');
      }
    } catch (e) {
      showToast('网络错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '个人资料',
      actions: [
        TextButton(
          onPressed: _isLoading ? null : _saveProfile,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text(
                  '保存',
                  style: TextStyle(
                    color: MColor.skin,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ],
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // 头像
              _buildAvatarSection(),
              const SizedBox(height: 40),

              // 昵称
              _buildFormField(
                label: '昵称',
                controller: _nicknameController,
                hintText: '请输入昵称',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入昵称';
                  }
                  if (value.trim().length > 20) {
                    return '昵称不能超过20个字符';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // 生日
              _buildDateField(),
              const SizedBox(height: 40),

              // 用户信息
              if (_user != null) _buildUserInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Column(
      children: [
        GestureDetector(
          onTap: _selectAvatar,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
              border: Border.all(
                color: MColor.skin.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: _avatarUrl != null && _avatarUrl!.isNotEmpty
                ? ClipOval(
                    child: Image.network(
                      _avatarUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.person,
                        size: 50,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.add_a_photo,
                    size: 40,
                    color: Colors.grey,
                  ),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          '点击更换头像',
          style: TextStyle(
            fontSize: 12,
            color: MColor.xFF777777,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: MColor.skin),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '生日',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectBirthdate,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _birthdateController.text.isEmpty ? '请选择生日' : _birthdateController.text,
                    style: TextStyle(
                      fontSize: 16,
                      color: _birthdateController.text.isEmpty ? Colors.grey[600] : MColor.xFF333333,
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '账号信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: MColor.xFF333333,
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow('用户ID', _user!.id.toString()),
          _buildInfoRow('UUID', _user!.uuid),
          _buildInfoRow('VIP状态', _user!.isVip ? '是' : '否'),
          _buildInfoRow('注册时间', _user!.createdAt?.split('T')[0] ?? '未知'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: MColor.xFF777777,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: MColor.xFF333333,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
