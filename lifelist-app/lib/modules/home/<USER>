import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/modules/home/<USER>';
import 'package:lifelist/tools/tools.dart';
import '../../constants/design.dart';

class TabbarPage extends StatefulWidget {
  final StatefulNavigationShell navigationShell;
  const TabbarPage(
    this.navigationShell, {
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('TabbarPage'));
  @override
  State createState() => _TabbarPageState();
}

class _TabbarPageState extends State<TabbarPage> {
  final List<dynamic> _tabs = [
    {'name': '广场', 'icon': 'assets/images/icon_detail_unselect.png', 'activeIcon': 'assets/images/icon_detail.png'},
    {'name': '清单', 'icon': 'assets/images/icon_plan_unselect.png', 'activeIcon': 'assets/images/icon_plan.png'},
    {'name': '我的', 'icon': 'assets/images/icon_mine_unselect.png', 'activeIcon': 'assets/images/icon_mine.png'}
  ];

  @override
  Widget build(BuildContext context) {
    debugPrint('qiazhun build tabbar page');
    return Scaffold(
      body: widget.navigationShell,
      bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          selectedItemColor: MColor.skin,
          unselectedItemColor: MColor.xFF999999,
          selectedLabelStyle: TextStyle(height: 1, fontSize: 12, fontWeight: FontWeight.w500),
          unselectedLabelStyle: TextStyle(height: 1, fontSize: 12),
          elevation: 0,
          items: _tabs.map((el) {
            return BottomNavigationBarItem(
              icon: Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Image.asset(
                  el['icon'],
                  height: 25,
                  width: 25,
                ),
              ),
              activeIcon: Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Image.asset(
                  el['activeIcon'],
                  height: 25,
                  width: 25,
                ),
              ),
              label: el['name'],
            );
          }).toList(),
          currentIndex: widget.navigationShell.currentIndex,
          onTap: (int index) {
            // 通过 context 发送事件（BlocProvider 已提供）
            // context.read<TabNavigationBloc>().add(TabSelected(index));
            logger.i('onTap ${index}');
            TabbarStateManager.instance.onTabbarChanged(index);
            widget.navigationShell.goBranch(
              index,
              initialLocation: index == widget.navigationShell.currentIndex,
            );
            setState(() {});
          }),
    );
  }
}
