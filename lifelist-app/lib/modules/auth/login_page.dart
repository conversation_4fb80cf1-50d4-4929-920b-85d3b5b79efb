import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/common/loading.dart';
import 'package:lifelist/common/user_manager.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/router/router.dart';
import 'package:lifelist/services/user_service.dart';
import 'package:lifelist/tools/tools.dart';
import 'package:lifelist/widgets/page_container_view.dart';
import 'package:sign_in_with_huawei/sign_in_with_huawei.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool _isLoading = false;

  bool _isAgreed = false;

  Future<void> _loginWithHuawei() async {
    if (_isLoading) return;

    if (!_isAgreed) {
      showToast('请先同意隐私政策和用户协议');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    Loading.show();

    try {
      final response = await SignInWithHuawei.instance.authById(
        forceLogin: true,
        idTokenAlg: IdTokenSignAlgorithm.PS256,
      );

      if (response.isSuccess) {
        final loginResp = await UserService().huaweiLogin(idToken: response.idToken ?? '', openid: response.openID ?? '');

        if (loginResp.code == 1 && loginResp.data != null) {
          // 保存用户信息到本地
          final token = loginResp.data!.token ?? '';
          if (token.isNotEmpty) {
            await UserManager.instance.saveUser(loginResp.data!, token);
          }

          showToast('登录成功');

          if (mounted) {
            // 返回到上一页或主页
            context.pop();
          }
        } else {
          showToast(loginResp.msg ?? '登录失败');
        }
      } else {
        showToast('华为登录失败: ${response.errorMsg}');
      }
    } catch (e) {
      showToast('登录失败: $e');
      logger.i('华为登录错误: $e');
    } finally {
      Loading.dismiss();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '登录',
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo或应用图标
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: MColor.skin.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Image.asset(
                'assets/images/icon_192.png',
                width: 60,
                height: 60,
                fit: BoxFit.scaleDown,
              ),
            ),

            const SizedBox(height: 40),

            // 应用名称
            const Text(
              '美瞬人生清单',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: MColor.xFF333333,
              ),
            ),

            const SizedBox(height: 8),

            // 应用描述
            const Text(
              '记录生活，成就梦想',
              style: TextStyle(
                fontSize: 16,
                color: MColor.xFF777777,
              ),
            ),

            const SizedBox(height: 80),

            // 华为登录按钮
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _loginWithHuawei,
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.account_circle,
                        color: Colors.white,
                      ),
                label: Text(
                  _isLoading ? '登录中...' : '登录华为账号',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: MColor.skin,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 2,
                ),
              ),
            ),

            const SizedBox(height: 40),

            // 用户协议和隐私政策
            RichText(
                text: TextSpan(
                    style: TextStyle(
                      fontSize: 14,
                      color: MColor.xFF777777,
                      height: 1.5,
                    ),
                    children: [
                  WidgetSpan(
                      child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _isAgreed = !_isAgreed;
                      });
                    },
                    child: Icon(
                      _isAgreed ? Icons.check_circle : Icons.radio_button_unchecked,
                      size: 16,
                      color: _isAgreed ? MColor.skin : MColor.xFF777777,
                    ),
                  )),
                  WidgetSpan(
                      child: const SizedBox(
                    width: 2,
                  )),
                  TextSpan(
                    text: '登录即表示您同意我们的',
                  ),
                  TextSpan(
                    text: '《隐私政策》',
                    style: TextStyle(
                      fontSize: 14,
                      color: MColor.skin,
                      height: 1.5,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/privacy', 'title': '隐私政策'});
                        // 处理点击事件
                      },
                  ),
                  TextSpan(text: '和'),
                  TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        fontSize: 14,
                        color: MColor.skin,
                        height: 1.5,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/useragreement', 'title': '用户协议'});
                        }),
                ])),
          ],
        ),
      ),
    );
  }
}
