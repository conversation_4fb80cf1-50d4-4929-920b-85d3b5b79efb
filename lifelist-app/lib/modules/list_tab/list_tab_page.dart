import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/common/user_manager.dart';
import 'package:lifelist/common/app_state_manager.dart';
import 'package:lifelist/modules/home/<USER>';
import 'package:lifelist/services/achivement_service.dart';
import '../../constants/design.dart';
import '../../widgets/page_container_view.dart';
import '../../services/api_service.dart';
import '../../models/list_model.dart';
import '../../tools/tools.dart';
import '../../router/router.dart';
import 'bloc/list_bloc.dart';
import 'bloc/list_event.dart';
import 'bloc/list_state.dart';
import '../home/<USER>';

class ListTabPage extends StatelessWidget {
  const ListTabPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ListBloc()..add(const LoadMyLists()),
      child: const _ListTabView(),
    );
  }
}

class _ListTabView extends StatefulWidget {
  const _ListTabView();

  @override
  State<_ListTabView> createState() => _ListTabViewState();
}

class _ListTabViewState extends State<_ListTabView> {
  void onTabActivated() {
    // 当切换到清单Tab时刷新数据
    context.read<ListBloc>().add(const RefreshMyLists());
  }

  void _onTabbarChanged() {
    if (mounted) {
      if (TabbarStateManager.instance.currentIndex == 1) {
        onTabActivated();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 监听登录状态变化
    AppStateManager.instance.addListener(_onLoginStateChanged);
    TabbarStateManager.instance.addListener(_onTabbarChanged);
  }

  @override
  void dispose() {
    AppStateManager.instance.removeListener(_onLoginStateChanged);
    TabbarStateManager.instance.removeListener(_onTabbarChanged);
    super.dispose();
  }

  void _onLoginStateChanged() {
    if (mounted) {
      // 如果用户登录了，加载数据
      if (AppStateManager.instance.isLoggedIn) {
        context.read<ListBloc>().add(const LoadMyLists());
      } else {
        // 如果用户退出登录，重新加载（会显示空状态）
        context.read<ListBloc>().add(const LoadMyLists());
      }
    }
  }

  void _createList() {
    if (!AppStateManager.instance.isLoggedIn) {
      context.push('/login').then((_) {
        if (AppStateManager.instance.isLoggedIn) {
          context.push(Routes.listCreatePath).then((_) {
            context.read<ListBloc>().add(const RefreshMyLists());
          });
        }
      });
      return;
    }
    context.push(Routes.listCreatePath).then((_) {
      context.read<ListBloc>().add(const RefreshMyLists());
    });
  }

  void _openListDetail(String listId) {
    context.push('${Routes.listDetailPath}/$listId').then((_) {
      context.read<ListBloc>().add(const RefreshMyLists());
    });
  }

  Widget _buildLoginPrompt() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/icon_192.png',
            width: 80,
            height: 80,
            fit: BoxFit.scaleDown,
          ),
          const SizedBox(height: 24),
          const Text(
            '请先登录',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: MColor.xFF333333,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '登录后即可创建和管理您的清单',
            style: TextStyle(
              fontSize: 14,
              color: MColor.xFF777777,
            ),
          ),
          const SizedBox(height: 32),
          GestureDetector(
            onTap: () {
              context.push('/login').then((_) {
                // 登录后重新加载数据
                if (AppStateManager.instance.isLoggedIn) {
                  context.read<ListBloc>().add(const LoadMyLists());
                }
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 16,
              ),
              decoration: BoxDecoration(
                color: MColor.skin,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: MColor.skin.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Text(
                '立即登录',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '我的清单',
      hideBack: true,
      actions: [
        IconButton(
          onPressed: _createList,
          icon: const Icon(
            Icons.add,
            color: MColor.xFF333333,
          ),
        ),
      ],
      body: !AppStateManager.instance.isLoggedIn
          ? _buildLoginPrompt()
          : BlocBuilder<ListBloc, ListState>(
              builder: (context, state) {
                if (state is ListLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is ListError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(state.message),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<ListBloc>().add(const LoadMyLists());
                          },
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  );
                } else if (state is ListEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.list_alt,
                          size: 64,
                          color: MColor.xFF999999,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '还没有清单',
                          style: TextStyle(
                            fontSize: 16,
                            color: MColor.xFF999999,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GestureDetector(
                          onTap: _createList,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: MColor.skin,
                              borderRadius: BorderRadius.circular(24),
                            ),
                            child: const Text(
                              '创建第一个清单',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                } else if (state is ListLoaded || state is ListDeleting || state is ListDeleteSuccess || state is ListDeleteError) {
                  List<AchivementModel> lists = [];
                  if (state is ListLoaded)
                    lists = state.lists;
                  else if (state is ListDeleting)
                    lists = state.lists;
                  else if (state is ListDeleteSuccess)
                    lists = state.lists;
                  else if (state is ListDeleteError) lists = state.lists;

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<ListBloc>().add(const RefreshMyLists());
                    },
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: lists.length,
                      itemBuilder: (context, index) {
                        final list = lists[index];
                        return _buildListCard(list);
                      },
                    ),
                  );
                }

                return const Center(
                  child: Text('未知状态'),
                );
              },
            ),
    );
  }

  Widget _buildListCard(AchivementModel list) {
    return GestureDetector(
      onTap: () => _openListDetail('${list.id}'),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        height: 160,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 背景图
              if (list.coverImage != null && list.coverImage!.isNotEmpty)
                Positioned.fill(
                  child: Image.network(
                    list.getCoverImage,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(
                          Icons.image,
                          size: 48,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                )
              else
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          MColor.skin.withOpacity(0.3),
                          MColor.skin.withOpacity(0.1),
                        ],
                      ),
                    ),
                  ),
                ),

              // 模糊遮罩
              Positioned.fill(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
                  child: Container(
                    color: Colors.black.withOpacity(0.4),
                  ),
                ),
              ),

              // 内容
              Positioned.fill(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题和进度
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              list.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    offset: Offset(1, 1),
                                    blurRadius: 2,
                                    color: Colors.black54,
                                  ),
                                ],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            list.progressText,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: MColor.skin,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // 描述
                      if (list.description.isNotEmpty)
                        Text(
                          list.description,
                          style: const TextStyle(
                            fontSize: 14,
                            color: MColor.xFF777777,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                      const SizedBox(height: 12),

                      // 进度条
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                '完成进度',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.white70,
                                ),
                              ),
                              Text(
                                '${(list.progress * 100).toInt()}%',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: list.progress,
                            backgroundColor: MColor.xFFECECEC,
                            valueColor: AlwaysStoppedAnimation<Color>(MColor.skin),
                            minHeight: 6,
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // 状态指示器
                      Row(
                        children: [
                          // 已完成项
                          Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: MColor.skin,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '已完成 ${list.completedCount}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(width: 16),

                          // 未完成项
                          Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: MColor.xFFECECEC,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '未完成 ${list.totalCount - list.completedCount}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: MColor.xFF777777,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
