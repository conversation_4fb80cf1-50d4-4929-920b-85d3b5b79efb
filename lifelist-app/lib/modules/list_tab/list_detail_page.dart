import 'package:flutter/material.dart';
import 'package:lifelist/common/loading.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/router/router.dart';
import 'package:lifelist/services/achivement_service.dart';
import 'package:lifelist/widgets/page_container_view.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/tools/tools.dart';
import 'package:lifelist/common/url_helper.dart';
import 'edit_list_page.dart';

class ListDetailPage extends StatefulWidget {
  final String listId;

  const ListDetailPage({
    Key? key,
    required this.listId,
  }) : super(key: key);

  @override
  State<ListDetailPage> createState() => _ListDetailPageState();
}

class _ListDetailPageState extends State<ListDetailPage> {
  AchivementModel? _listDetail;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadListDetail();
  }

  Future<void> _loadListDetail() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await AchivementService().getListDetail(widget.listId);
      if (response.code == 1 && response.data != null) {
        setState(() {
          _listDetail = response.data;
          _isLoading = false;
        });
      } else {
        showToast(response.msg ?? '获取清单详情失败');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      showToast('网络错误: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleItemCompletion(AchivementItemModel item) async {
    try {
      final response = await AchivementService().toggleItem('${item.id}');
      if (response.code == 1) {
        showToast(response.msg ?? '状态更新成功');
        _loadListDetail(); // 刷新数据
      } else {
        showToast(response.msg ?? '状态更新失败');
      }
    } catch (e) {
      showToast('网络错误: $e');
    }
  }

  Future<void> _editItemName(AchivementItemModel item) async {
    final TextEditingController controller = TextEditingController(text: item.name);

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑子项'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: '请输入子项名称',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(controller.text.trim()),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty && result != item.name) {
      try {
        final response = await AchivementService().updateItem(
          itemId: '${item.id}',
          name: result,
        );
        if (response.code == 1) {
          showToast('更新成功');
          _loadListDetail(); // 刷新数据
        } else {
          showToast(response.msg ?? '更新失败');
        }
      } catch (e) {
        showToast('网络错误: $e');
      }
    }
  }

  Future<void> _addNewItem() async {
    TextEditingController textController = TextEditingController(text: '');
    Widget textField = TextField(
      controller: textController,
      keyboardType: TextInputType.text,
      decoration: InputDecoration(
        hintText: '请输入子项名称',
        hintStyle: TextStyle(fontSize: 14, color: MColor.xFF999999, height: 1.4),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
        isDense: true,
      ),
    );

    RouterHelper.router.pushNamed(Routes.customPopupPath, extra: {
      'title': '添加新子项',
      'widget': textField,
      'onConfirm': () async {
        String result = textController.text;
        if (result != null && result.isNotEmpty) {
          Loading.show();
          try {
            final response = await AchivementService().addItem(
              listId: widget.listId,
              name: result,
            );
            if (response.code == 1) {
              showToast('添加成功');
              _loadListDetail(); // 刷新数据
            } else {
              showToast(response.msg ?? '添加失败');
            }
          } catch (e) {
            showToast('网络错误: $e');
          } finally {
            Loading.dismiss();
          }
        }
      }
    }).then((_) {
      textController.dispose();
    });
  }

  Future<void> _shareList() async {
    if (_listDetail == null) return;
    if (_listDetail!.squareId != null) {
      showToast('该清单已经分享到广场');
      return;
    }
    try {
      final response = await AchivementService().shareToSquare(int.parse(widget.listId));
      if (response.code == 1) {
        showToast(response.msg ?? '分享成功');
        _loadListDetail(); // 刷新数据
      } else {
        showToast(response.msg ?? '分享失败');
      }
    } catch (e) {
      showToast('网络错误: $e');
    }
  }

  Future<void> _editList() async {
    if (_listDetail == null) return;

    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => EditListPage(listDetail: _listDetail!),
      ),
    );

    // 如果编辑页面返回true，表示有更新，需要刷新数据
    if (result == true) {
      _loadListDetail();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: _listDetail?.name ?? '清单详情',
      actions: [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: _editList,
        ),
        if (_listDetail != null && _listDetail!.squareId == null) IconButton(onPressed: _shareList, icon: Icon(Icons.share))
      ],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _listDetail == null
              ? const Center(child: Text('清单不存在'))
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    final list = _listDetail!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 背景图
          _buildCoverImage(list),
          const SizedBox(height: 16),

          // 描述
          _buildDescription(list),
          const SizedBox(height: 16),

          // 进度
          _buildProgress(list),
          const SizedBox(height: 24),

          // 子项列表
          _buildItemsList(list),
          const SizedBox(height: 16),

          // 添加按钮
          _buildAddButton(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildCoverImage(AchivementModel list) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: MColor.skin.withOpacity(0.1),
      ),
      child: list.getCoverImage.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                list.getCoverImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
              ),
            )
          : _buildPlaceholderImage(),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            MColor.skin.withOpacity(0.3),
            MColor.skin.withOpacity(0.1),
          ],
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.image,
          size: 48,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildDescription(AchivementModel list) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          list.description ?? '暂无描述',
          style: const TextStyle(
            fontSize: 14,
            color: MColor.xFF777777,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildProgress(AchivementModel list) {
    final progress = list.progress;
    final completedCount = list.completedCount;
    final totalCount = list.totalCount;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '完成进度',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: MColor.xFF333333,
              ),
            ),
            Text(
              '$completedCount/$totalCount',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.skin,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(MColor.skin),
          minHeight: 8,
        ),
        const SizedBox(height: 8),
        Text(
          '${(progress * 100).toInt()}% 完成',
          style: const TextStyle(
            fontSize: 12,
            color: MColor.xFF777777,
          ),
        ),
      ],
    );
  }

  Widget _buildItemsList(AchivementModel list) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '子项列表',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 12),
        if (list.items?.isEmpty == true)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                SizedBox(height: 8),
                Text(
                  '暂无子项',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          )
        else
          ...list.items!.map((item) => _buildItemCard(item)).toList(),
      ],
    );
  }

  Widget _buildItemCard(AchivementItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _toggleItemCompletion(item),
          onLongPress: () => _editItemName(item),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: item.isCompleted ? Colors.green[100] : Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: item.isCompleted ? Colors.green : Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  item.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: item.isCompleted ? Colors.green[700] : Colors.grey[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item.name,
                    style: TextStyle(
                      fontSize: 16,
                      color: item.isCompleted ? Colors.green[800] : MColor.xFF333333,
                      decoration: item.isCompleted ? TextDecoration.lineThrough : null,
                      fontWeight: item.isCompleted ? FontWeight.normal : FontWeight.w500,
                    ),
                  ),
                ),
                if (item.isCompleted)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      '已完成',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _addNewItem,
        icon: const Icon(Icons.add),
        label: const Text('添加新子项'),
        style: ElevatedButton.styleFrom(
          backgroundColor: MColor.skin,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }
}
