import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lifelist/services/achivement_service.dart';
import 'package:lifelist/common/app_state_manager.dart';
import 'package:lifelist/common/toast_utils.dart';
import 'package:lifelist/tools/tools.dart';
import 'list_event.dart';
import 'list_state.dart';

class ListBloc extends Bloc<ListEvent, ListState> {
  final AchivementService _achivementService;

  ListBloc({AchivementService? achivementService})
      : _achivementService = achivementService ?? AchivementService(),
        super(const ListInitial()) {
    on<LoadMyLists>(_onLoadMyLists);
    on<RefreshMyLists>(_onRefreshMyLists);
    on<DeleteList>(_onDeleteList);
  }

  Future<void> _onLoadMyLists(
    LoadMyLists event,
    Emitter<ListState> emit,
  ) async {
    // 检查登录状态
    if (!AppStateManager.instance.isLoggedIn) {
      emit(const ListEmpty());
      return;
    }

    emit(const ListLoading());
    try {
      final response = await _achivementService.getMyLists();
      if (response.code == 1 && response.data != null) {
        if (response.data!.isEmpty) {
          emit(const ListEmpty());
        } else {
          emit(ListLoaded(response.data!));
        }
      } else {
        emit(ListError(response.msg ?? '加载失败'));
      }
    } catch (e) {
      emit(ListError('加载失败: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshMyLists(
    RefreshMyLists event,
    Emitter<ListState> emit,
  ) async {
    // 检查登录状态
    if (!AppStateManager.instance.isLoggedIn) {
      emit(const ListEmpty());
      return;
    }

    try {
      final response = await _achivementService.getMyLists();
      if (response.code == 1 && response.data != null) {
        if (response.data!.isEmpty) {
          emit(const ListEmpty());
        } else {
          emit(ListLoaded(response.data!));
        }
      } else {
        emit(ListError(response.msg ?? '刷新失败'));
      }
    } catch (e) {
      emit(ListError('刷新失败: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteList(
    DeleteList event,
    Emitter<ListState> emit,
  ) async {
    final currentState = state;
    if (currentState is ListLoaded) {
      emit(ListDeleting(currentState.lists, event.listId));

      try {
        final response = await _achivementService.deleteList(event.listId.toString());
        if (response.code == 1) {
          showToast(response.msg ?? '删除成功');
          emit(ListDeleteSuccess(currentState.lists, response.msg ?? '删除成功'));

          // 刷新数据
          add(const RefreshMyLists());
        } else {
          showToast(response.msg ?? '删除失败');
          emit(ListDeleteError(currentState.lists, response.msg ?? '删除失败'));
        }
      } catch (e) {
        final errorMessage = '删除失败: ${e.toString()}';
        showToast(errorMessage);
        emit(ListDeleteError(currentState.lists, errorMessage));
      }
    }
  }
}
