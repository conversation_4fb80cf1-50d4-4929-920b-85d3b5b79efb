import 'package:equatable/equatable.dart';
import 'package:lifelist/models/list_model.dart';

abstract class ListState extends Equatable {
  const ListState();

  @override
  List<Object> get props => [];
}

class ListInitial extends ListState {
  const ListInitial();
}

class ListLoading extends ListState {
  const ListLoading();
}

class ListLoaded extends ListState {
  final List<AchivementModel> lists;

  const ListLoaded(this.lists);

  @override
  List<Object> get props => [lists];
}

class ListError extends ListState {
  final String message;

  const ListError(this.message);

  @override
  List<Object> get props => [message];
}

class ListEmpty extends ListState {
  const ListEmpty();
}

class ListDeleting extends ListState {
  final List<AchivementModel> lists;
  final int deletingListId;

  const ListDeleting(this.lists, this.deletingListId);

  @override
  List<Object> get props => [lists, deletingListId];
}

class ListDeleteSuccess extends ListState {
  final List<AchivementModel> lists;
  final String message;

  const ListDeleteSuccess(this.lists, this.message);

  @override
  List<Object> get props => [lists, message];
}

class ListDeleteError extends ListState {
  final List<AchivementModel> lists;
  final String message;

  const ListDeleteError(this.lists, this.message);

  @override
  List<Object> get props => [lists, message];
}
