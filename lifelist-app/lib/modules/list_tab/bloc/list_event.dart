import 'package:equatable/equatable.dart';

abstract class ListEvent extends Equatable {
  const ListEvent();

  @override
  List<Object> get props => [];
}

class LoadMyLists extends ListEvent {
  const LoadMyLists();
}

class RefreshMyLists extends ListEvent {
  const RefreshMyLists();
}

class DeleteList extends ListEvent {
  final int listId;

  const DeleteList(this.listId);

  @override
  List<Object> get props => [listId];
}
