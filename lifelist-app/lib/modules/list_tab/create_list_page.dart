import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/services/achivement_service.dart';
import '../../constants/design.dart';
import '../../widgets/page_container_view.dart';
import '../../services/api_service.dart';
import '../../tools/tools.dart';
import '../../common/image_picker_helper.dart';

class CreateListPage extends StatefulWidget {
  const CreateListPage({super.key});

  @override
  State<CreateListPage> createState() => _CreateListPageState();
}

class _CreateListPageState extends State<CreateListPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  bool _isLoading = false;
  String? _coverImageUrl;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _createList() async {
    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();

    if (name.isEmpty) {
      showToast('请输入清单名称');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final response = await AchivementService().createList(
        name: name,
        description: description,
        coverImage: _coverImageUrl,
      );

      if (response.code == 1) {
        showToast(response.msg ?? '创建成功');
        if (mounted) {
          context.pop();
        }
      } else {
        showToast(response.msg ?? '创建失败');
      }
    } catch (e) {
      showToast('创建失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectCoverImage() async {
    final imageUrl = await ImagePickerHelper.showImagePickerDialog(context);
    if (imageUrl != null) {
      setState(() {
        _coverImageUrl = imageUrl;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '创建清单',
      actions: [
        TextButton(
          onPressed: _isLoading ? null : _createList,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(MColor.skin),
                  ),
                )
              : const Text(
                  '创建',
                  style: TextStyle(
                    fontSize: 16,
                    color: MColor.skin,
                    fontWeight: FontWeight.w500,
                  ),
                ),
        ),
      ],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 清单名称
            const Text(
              '清单名称',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.xFF333333,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: '例如：我去过的城市',
                hintStyle: const TextStyle(
                  color: MColor.xFF999999,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.xFFECECEC,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.xFFECECEC,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.skin,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLength: 50,
            ),

            const SizedBox(height: 24),

            // 清单描述
            const Text(
              '清单描述',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.xFF333333,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: '描述一下这个清单的内容...',
                hintStyle: const TextStyle(
                  color: MColor.xFF999999,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.xFFECECEC,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.xFFECECEC,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: MColor.skin,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 4,
              maxLength: 200,
            ),

            const SizedBox(height: 24),

            // 背景图片
            const Text(
              '背景图片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MColor.xFF333333,
              ),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: _selectCoverImage,
              child: Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: MColor.xFFECECEC,
                    style: BorderStyle.solid,
                  ),
                ),
                child: _coverImageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          _coverImageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.broken_image,
                                  size: 32,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    : const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_photo_alternate,
                              size: 32,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 4),
                            Text(
                              '点击选择背景图片',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 32),

            // 提示信息
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: MColor.xFFF4FEFA,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: MColor.skin.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: MColor.skin,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '温馨提示',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: MColor.xFF333333,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• 创建后可以添加子项并标记完成状态\n• 可以分享清单给朋友一起完成',
                    style: TextStyle(
                      fontSize: 14,
                      color: MColor.xFF777777,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
