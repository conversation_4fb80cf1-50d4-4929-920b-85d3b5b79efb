import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/services/achivement_service.dart';
import 'package:lifelist/widgets/page_container_view.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/tools/tools.dart';
import 'package:lifelist/common/image_picker_helper.dart';
import 'package:lifelist/common/url_helper.dart';

class EditListPage extends StatefulWidget {
  final AchivementModel listDetail;

  const EditListPage({
    Key? key,
    required this.listDetail,
  }) : super(key: key);

  @override
  State<EditListPage> createState() => _EditListPageState();
}

class _EditListPageState extends State<EditListPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  bool _isLoading = false;
  String? _coverImageUrl;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.listDetail.name);
    _descriptionController = TextEditingController(text: widget.listDetail.description ?? '');
    _coverImageUrl = UrlHelper.getFullImageUrl(widget.listDetail.coverImage);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AchivementService().updateList(
        listId: '${widget.listDetail.id}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        coverImage: _coverImageUrl,
      );

      if (response.code == 1) {
        showToast(response.msg ?? '更新成功');
        if (mounted) {
          context.pop(true); // 返回true表示有更新
        }
      } else {
        showToast(response.msg ?? '更新失败');
      }
    } catch (e) {
      showToast('网络错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectCoverImage() async {
    final imageUrl = await ImagePickerHelper.showImagePickerDialog(context);
    if (imageUrl != null) {
      setState(() {
        _coverImageUrl = imageUrl;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageContainerView(
      title: '编辑清单',
      actions: [
        TextButton(
          onPressed: _isLoading ? null : _saveChanges,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text(
                  '保存',
                  style: TextStyle(
                    color: MColor.skin,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ],
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 清单名称
              _buildFormField(
                label: '清单名称',
                controller: _nameController,
                hintText: '请输入清单名称',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入清单名称';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // 清单描述
              _buildFormField(
                label: '清单描述',
                controller: _descriptionController,
                hintText: '请输入清单描述（可选）',
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // 背景图片
              const Text(
                '背景图片',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: MColor.xFF333333,
                ),
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: _selectCoverImage,
                child: Container(
                  width: double.infinity,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey[300]!,
                    ),
                  ),
                  child: _coverImageUrl != null && _coverImageUrl!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            _coverImageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.broken_image,
                                    size: 32,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    '图片加载失败',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_photo_alternate,
                                size: 32,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 4),
                              Text(
                                '点击选择背景图片',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 24),

              // 删除清单按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _showDeleteConfirmation,
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  label: const Text(
                    '删除清单',
                    style: TextStyle(color: Colors.red),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: MColor.xFF333333,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: MColor.skin),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个清单吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteList();
            },
            child: const Text(
              '删除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteList() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AchivementService().deleteList('${widget.listDetail.id}');
      if (response.code == 1) {
        showToast(response.msg ?? '删除成功');
        if (mounted) {
          context.pop();
          context.pop(); // 返回到清单列表页面
        }
      } else {
        showToast(response.msg ?? '删除失败');
      }
    } catch (e) {
      showToast('网络错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
