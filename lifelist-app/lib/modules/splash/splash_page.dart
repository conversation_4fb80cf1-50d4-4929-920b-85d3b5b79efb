import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/constants/design.dart';
import 'package:lifelist/router/router.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  static const String _privacyAgreedKey = 'privacy_policy_agreed';

  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;

  bool _showPrivacyDialog = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _checkPrivacyAgreement();
  }

  void _initAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _textController.forward();
    });
  }

  Future<void> _checkPrivacyAgreement() async {
    await Future.delayed(const Duration(milliseconds: 2000)); // 显示闪屏动画

    final prefs = await SharedPreferences.getInstance();
    final hasAgreed = prefs.getBool(_privacyAgreedKey) ?? false;

    setState(() {
      _isLoading = false;
      _showPrivacyDialog = !hasAgreed;
    });

    if (hasAgreed) {
      _navigateToMain();
    }
  }

  Future<void> _agreePrivacyPolicy() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_privacyAgreedKey, true);

    setState(() {
      _showPrivacyDialog = false;
    });

    _navigateToMain();
  }

  void _navigateToMain() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        RouterHelper.router.go(Routes.squareTabPath);
        // context.go('/');
      }
    });
  }

  void _exitApp() {
    // 在实际应用中，这里可以调用系统API退出应用
    // 或者显示一个提示，告诉用户需要同意才能使用
    SystemNavigator.pop();
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // 主要内容
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo动画
                AnimatedBuilder(
                  animation: _logoAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _logoAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: MColor.skin.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(60),
                          boxShadow: [
                            BoxShadow(
                              color: MColor.skin.withOpacity(0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Image.asset(
                          'assets/images/icon_white.png',
                          width: 60,
                          height: 60,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 30),

                // 文字动画
                AnimatedBuilder(
                  animation: _textAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _textAnimation.value,
                      child: Column(
                        children: [
                          const Text(
                            '美瞬人生清单',
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: MColor.xFF333333,
                              letterSpacing: 2,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '记录生活，成就梦想',
                            style: TextStyle(
                              fontSize: 16,
                              color: MColor.xFF777777,
                              letterSpacing: 1,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(height: 50),

                // 加载指示器
                if (_isLoading)
                  const SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(MColor.skin),
                    ),
                  ),
              ],
            ),
          ),

          // 隐私政策对话框
          if (_showPrivacyDialog)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(24),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.privacy_tip,
                        size: 48,
                        color: MColor.skin,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '隐私政策和用户协议',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: MColor.xFF333333,
                        ),
                      ),
                      const SizedBox(height: 16),
                      RichText(
                          text: TextSpan(
                              style: TextStyle(
                                fontSize: 14,
                                color: MColor.xFF777777,
                                height: 1.5,
                              ),
                              children: [
                            TextSpan(
                              text: '欢迎使用美瞬人生清单!\n\n为了更好地为您提供服务，请您仔细阅读并同意我们的',
                            ),
                            TextSpan(
                              text: '《隐私政策》',
                              style: TextStyle(
                                fontSize: 14,
                                color: MColor.skin,
                                height: 1.5,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/privacy', 'title': '隐私政策'});
                                  // 处理点击事件
                                },
                            ),
                            TextSpan(text: '和'),
                            TextSpan(
                                text: '《用户协议》',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: MColor.skin,
                                  height: 1.5,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    context.push('${Routes.webPath}', extra: {'url': 'https://lifelist.ceweng.com/useragreement', 'title': '用户协议'});
                                  }),
                            TextSpan(text: '。\n\n我们承诺保护您的个人信息安全，仅在必要时收集和使用您的数据。'),
                          ])),
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _exitApp,
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: MColor.xFF999999),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text(
                                '不同意',
                                style: TextStyle(
                                  color: MColor.xFF999999,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _agreePrivacyPolicy,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: MColor.skin,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                elevation: 0,
                              ),
                              child: const Text(
                                '同意并继续',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
