extension DateTimeExtension on DateTime {
  DateTime lastYear() {
    return DateTime(year - 1, month, day);
  }

  DateTime nextYear() {
    return DateTime(year + 1, month, day);
  }

  DateTime lastWeek() {
    return DateTime(year, month, day - 7, hour, minute, second, millisecond, microsecond);
  }

  DateTime daysAgo(int days) {
    return DateTime(year, month, day - days, hour, minute, second, millisecond, microsecond);
  }

  DateTime yesterday() {
    return DateTime(year, month, day - 1, hour, minute, second, millisecond, microsecond);
  }

  DateTime lastMinute() {
    return DateTime(year, month, day, hour, minute - 1, second, millisecond, microsecond);
  }

  DateTime lastHour() {
    return DateTime(year, month, day, hour - 1, minute, second, millisecond, microsecond);
  }

  DateTime today() {
    return DateTime(year, month, day);
  }

  DateTime tomorrow() {
    return DateTime(year, month, day + 1);
  }

  bool isSameDay(DateTime? other) {
    return other != null && year == other.year && month == other.month && day == other.day;
  }

  String toYMDString() {
    return '$year-$month-$day';
  }
}
