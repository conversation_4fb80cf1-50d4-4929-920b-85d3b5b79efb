import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lifelist/common/file_repo.dart';
import 'package:lifelist/common/url_helper.dart';
import 'package:lifelist/tools/tools.dart';

class ImagePickerHelper {
  static final ImagePicker _picker = ImagePicker();

  /// 显示图片选择对话框
  static Future<String?> showImagePickerDialog(BuildContext context) async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择图片'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('拍照'),
                onTap: () async {
                  // Navigator.of(context).pop();
                  final imageUrl = await _pickImageFromCamera();
                  if (context.mounted) {
                    Navigator.of(context).pop(imageUrl);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择'),
                onTap: () async {
                  // Navigator.of(context).pop();
                  final imageUrl = await _pickImageFromGallery();
                  if (context.mounted) {
                    Navigator.of(context).pop(imageUrl);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 从相机拍照
  static Future<String?> _pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 80,
      );

      if (image != null) {
        return await _uploadImage(image.path);
      }
    } catch (e) {
      showToast('拍照失败: $e');
    }
    return null;
  }

  /// 从相册选择
  static Future<String?> _pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 80,
      );

      if (image != null) {
        return await _uploadImage(image.path);
      }
    } catch (e) {
      showToast('选择图片失败: $e');
    }
    return null;
  }

  /// 上传图片到服务器
  static Future<String?> _uploadImage(String filePath) async {
    try {
      showToast('正在上传图片...');

      // 检查文件是否存在
      final file = File(filePath);
      if (!await file.exists()) {
        showToast('文件不存在');
        return null;
      }

      // 检查文件大小（限制为5MB）
      final fileSize = await file.length();
      if (fileSize > 5 * 1024 * 1024) {
        showToast('图片文件过大，请选择小于5MB的图片');
        return null;
      }

      final response = await FileRepo.fileUpload(filePath);

      if (response.code == 1) {
        showToast('图片上传成功');
        // 假设服务器返回的数据格式为 { "url": "图片URL" }
        if (response.data is Map<String, dynamic>) {
          final data = response.data as Map<String, dynamic>;
          final url = data['url'] as String?;
          return url != null ? UrlHelper.getFullImageUrl(url) : null;
        }
        // 如果服务器直接返回URL字符串
        final url = response.data.toString();
        return UrlHelper.getFullImageUrl(url);
      } else {
        showToast(response.msg ?? '图片上传失败');
        return null;
      }
    } catch (e) {
      showToast('图片上传失败: $e');
      return null;
    }
  }

  /// 显示加载对话框
  static void showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在上传...'),
            ],
          ),
        );
      },
    );
  }

  /// 隐藏加载对话框
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }
}
