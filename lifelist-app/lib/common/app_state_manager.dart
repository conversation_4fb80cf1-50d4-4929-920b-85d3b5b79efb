import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:lifelist/models/user_model.dart';

/// 全局应用状态管理器
/// 用于管理登录状态变化的通知
class AppStateManager extends ChangeNotifier {
  static AppStateManager? _instance;
  static AppStateManager get instance => _instance ??= AppStateManager._();
  
  AppStateManager._();
  
  UserModel? _currentUser;
  bool _isLoggedIn = false;
  
  /// 当前用户
  UserModel? get currentUser => _currentUser;
  
  /// 是否已登录
  bool get isLoggedIn => _isLoggedIn;
  
  /// 更新登录状态
  void updateLoginState(UserModel? user) {
    final wasLoggedIn = _isLoggedIn;
    final oldUser = _currentUser;
    
    _currentUser = user;
    _isLoggedIn = user != null;
    
    // 只有状态真正发生变化时才通知
    if (wasLoggedIn != _isLoggedIn || oldUser?.id != user?.id) {
      notifyListeners();
      debugPrint('登录状态已更新: ${_isLoggedIn ? "已登录" : "未登录"}');
    }
  }
  
  /// 清除登录状态
  void clearLoginState() {
    if (_isLoggedIn || _currentUser != null) {
      _currentUser = null;
      _isLoggedIn = false;
      notifyListeners();
      debugPrint('登录状态已清除');
    }
  }
  
  /// 刷新用户信息（不改变登录状态）
  void updateUserInfo(UserModel user) {
    if (_currentUser?.id == user.id) {
      _currentUser = user;
      notifyListeners();
      debugPrint('用户信息已更新');
    }
  }
}
