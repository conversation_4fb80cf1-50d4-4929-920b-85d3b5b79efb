import 'package:dio/dio.dart';
import 'package:lifelist/common/base_model.dart';
import 'package:lifelist/services/api_service.dart';

class FileRepo {
  static Future<BaseModel<dynamic>> fileUpload(String filePath) async {
    return BaseModel.fromJson(
        await ApiService().postForm('common/upload', data: {"file": await MultipartFile.fromFile(filePath, filename: filePath)}), (json) => json);
  }
}
