import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lifelist/models/user_model.dart';
import 'package:lifelist/common/app_state_manager.dart';

class UserManager {
  static const String _userKey = 'user_info';
  static const String _tokenKey = 'user_token';

  static UserManager? _instance;
  static UserManager get instance => _instance ??= UserManager._();

  UserManager._();

  UserModel? _currentUser;
  String? _currentToken;

  /// 获取当前用户
  UserModel? get currentUser => _currentUser;

  /// 获取当前token
  String? get currentToken => _currentToken;

  /// 是否已登录
  bool get isLoggedIn => _currentUser != null && _currentToken != null;

  /// 初始化用户管理器，从本地存储加载用户信息
  Future<void> init() async {
    await _loadUserFromLocal();
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载token
      _currentToken = prefs.getString(_tokenKey);

      // 加载用户信息
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = UserModel.fromJson(userMap);
      }

      // 同步状态到全局状态管理器
      AppStateManager.instance.updateLoginState(_currentUser);
    } catch (e) {
      print('加载用户信息失败: $e');
      await clearUser();
    }
  }

  /// 保存用户信息到本地
  Future<void> saveUser(UserModel user, String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存token
      await prefs.setString(_tokenKey, token);
      _currentToken = token;

      // 保存用户信息
      final userJson = jsonEncode(user.toJson());
      await prefs.setString(_userKey, userJson);
      _currentUser = user;

      // 同步状态到全局状态管理器
      AppStateManager.instance.updateLoginState(_currentUser);

      print('用户信息保存成功');
    } catch (e) {
      print('保存用户信息失败: $e');
    }
  }

  /// 更新用户信息
  Future<void> updateUser(UserModel user) async {
    if (_currentToken == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = jsonEncode(user.toJson());
      await prefs.setString(_userKey, userJson);
      _currentUser = user;

      // 同步状态到全局状态管理器
      AppStateManager.instance.updateUserInfo(user);

      print('用户信息更新成功');
    } catch (e) {
      print('更新用户信息失败: $e');
    }
  }

  /// 清除用户信息
  Future<void> clearUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await prefs.remove(_tokenKey);

      _currentUser = null;
      _currentToken = null;

      // 同步状态到全局状态管理器
      AppStateManager.instance.clearLoginState();

      print('用户信息已清除');
    } catch (e) {
      print('清除用户信息失败: $e');
    }
  }

  /// 检查token是否有效
  bool isTokenValid() {
    if (_currentToken == null || _currentToken!.isEmpty) {
      return false;
    }

    // 这里可以添加token过期检查逻辑
    // 比如解析JWT token的过期时间

    return true;
  }

  /// 当收到token失效响应时调用
  Future<void> onTokenExpired() async {
    print('Token已失效，清除用户信息');
    await clearUser();
  }
}
