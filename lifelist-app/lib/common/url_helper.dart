import 'package:lifelist/services/api_service.dart';

class UrlHelper {
  /// 获取完整的图片URL
  /// 如果传入的是相对路径（以/开头），则拼接基础URL
  /// 如果传入的是完整URL（包含http/https），则直接返回
  /// 如果传入的是null或空字符串，则返回空字符串
  static String getFullImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return '';
    }

    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // 如果是相对路径，拼接基础URL
    if (imagePath.startsWith('/')) {
      final baseUrl = ApiService.kBaseUrl;
      // 移除baseUrl末尾的/api/v1部分，只保留域名和端口
      final domainUrl = baseUrl.replaceAll('/api/v1', '');
      return '$domainUrl$imagePath';
    }

    // 其他情况直接返回
    return imagePath;
  }

  /// 检查URL是否为有效的图片URL
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    final fullUrl = getFullImageUrl(url);
    if (fullUrl.isEmpty) {
      return false;
    }

    // 检查是否为支持的图片格式
    final supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    final lowerUrl = fullUrl.toLowerCase();

    return supportedExtensions.any((ext) => lowerUrl.contains(ext));
  }

  /// 获取占位符图片URL（可以是本地资源或默认图片）
  static String getPlaceholderImageUrl() {
    return 'assets/images/placeholder.png'; // 可以根据需要修改
  }
}
