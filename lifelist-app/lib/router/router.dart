import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lifelist/modules/square_tab/square_detail_page.dart';
import 'package:lifelist/router/app_router_observer.dart';
import 'package:lifelist/router/dialog_page.dart';
import 'package:lifelist/screens/web_page.dart';
import 'package:lifelist/widgets/custom_popup_panel.dart';

import '../modules/home/<USER>';
import '../modules/square_tab/square_tab_page.dart';
import '../modules/list_tab/list_tab_page.dart';
import '../modules/mine_tab/mine_tab_page.dart';
import '../modules/list_tab/create_list_page.dart';
import '../modules/list_tab/list_detail_page.dart';
import '../modules/list_tab/edit_list_page.dart';
import '../modules/auth/login_page.dart';
import '../modules/mine_tab/profile_page.dart';
import '../modules/splash/splash_page.dart';

class Routes {
  static const splashPath = '/';
  static const indexPath = '/home';
  static const squareTabPath = '/square';
  static const squareDetailPath = '/square-detail';
  static const listTabPath = '/list';
  static const mineTabPath = '/mine';
  static const listDetailPath = '/list-detail';
  static const listCreatePath = '/list-create';
  static const listEditPath = '/list-edit';
  static const remainingPath = '/remaining';

  static const searchPath = '/search';

  static const popupPath = '/popup';
  static const customPopupPath = '/custom-popup';

  static const loginPath = '/login';
  static const profilePath = '/profile';
  static const webPath = '/web';
}

class RouterHelper {
  static final RouterHelper _instance = RouterHelper._internal();

  static RouterHelper get instance => _instance;

  static late final GoRouter router;

  static RouteObserver<PageRoute> routeObserver = AppRouteObserver();

  static final GlobalKey<NavigatorState> _sectionANavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'sectionANav');
  static final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'rootNav');

  static final GlobalKey<NavigatorState> parentNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> homeTabNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> searchTabNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> settingsTabNavigatorKey = GlobalKey<NavigatorState>();

  BuildContext get context => router.routerDelegate.navigatorKey.currentContext!;

  GoRouterDelegate get routerDelegate => router.routerDelegate;

  GoRouteInformationParser get routeInformationParser => router.routeInformationParser;

  // static const String signUpPath = '/signUp';
  // static const String signInPath = '/signIn';
  // static const String detailPath = '/detail';
  // static const String rootDetailPath = '/rootDetail';

  // static const String homePath = '/home';
  // static const String settingsPath = '/settings';
  // static const String searchPath = '/search';

  factory RouterHelper() {
    return _instance;
  }

  RouterHelper._internal() {
    final routes = [
      // 闪屏页面路由
      GoRoute(
        path: Routes.splashPath,
        name: Routes.splashPath,
        builder: (context, state) => const SplashPage(),
      ),

      // 主页面路由 - 重定向到清单页面
      GoRoute(
        path: Routes.indexPath,
        redirect: (context, state) => Routes.listTabPath,
      ),

      StatefulShellRoute.indexedStack(
        parentNavigatorKey: parentNavigatorKey,
        branches: [
          StatefulShellBranch(
            navigatorKey: _sectionANavigatorKey,
            routes: <RouteBase>[
              GoRoute(
                path: Routes.squareTabPath,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return getPage(
                    child: const SquareTabPage(),
                    state: state,
                  );
                },
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: Routes.listTabPath,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return getPage(
                    child: const ListTabPage(),
                    state: state,
                  );
                },
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: Routes.mineTabPath,
                builder: (BuildContext context, GoRouterState state) => const MineTabPage(),
              ),
            ],
          ),
        ],
        pageBuilder: (
          BuildContext context,
          GoRouterState state,
          StatefulNavigationShell navigationShell,
        ) {
          return getPage(
            child: TabbarPage(
              navigationShell,
            ),
            state: state,
          );
        },
      ),
      // GoRoute(
      //   parentNavigatorKey: parentNavigatorKey,
      //   path: Routes.INDEX,
      //   builder: (context, state) => const TabbarPage(),
      // ),
      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.listCreatePath,
        name: Routes.listCreatePath,
        builder: (context, state) => const CreateListPage(),
      ),

      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: '${Routes.squareDetailPath}/:listId',
        name: Routes.squareDetailPath,
        builder: (context, state) {
          final listId = state.pathParameters['listId']!;
          return SquareDetailPage(listId: listId);
        },
      ),
      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: '${Routes.listDetailPath}/:listId',
        name: Routes.listDetailPath,
        builder: (context, state) {
          final listId = state.pathParameters['listId']!;
          return ListDetailPage(listId: listId);
        },
      ),

      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.remainingPath,
        name: Routes.remainingPath,
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.loginPath,
        name: Routes.loginPath,
        builder: (context, state) => const LoginPage(),
      ),

      GoRoute(
        parentNavigatorKey: parentNavigatorKey,
        path: Routes.profilePath,
        name: Routes.profilePath,
        builder: (context, state) => const ProfilePage(),
      ),

      GoRoute(
        path: Routes.customPopupPath,
        name: Routes.customPopupPath,
        pageBuilder: (context, state) {
          String title = (state.extra as Map)['title'];
          Widget? widget = (state.extra as Map)['widget'];
          dynamic onConfirm = (state.extra as Map)['onConfirm'];
          double? heightFraction = (state.extra as Map)['heightFraction'];
          return ModalPage(
              heightFraction: heightFraction,
              child: CustomPopupPanel(
                title: title,
                widget: widget,
                onConfirm: onConfirm,
                heightFraction: heightFraction,
              ));
        },
      ),
      GoRoute(
        path: Routes.popupPath,
        name: Routes.popupPath,
        pageBuilder: (context, state) {
          AppBar? appBar = (state.extra as Map)['appBar'];
          Widget widget = (state.extra as Map)['widget'];
          List<Widget>? buttons = (state.extra as Map)['buttons'];
          return DialogPage(builder: (_) => PopupPanel(widget, appBar: appBar, footerButtons: buttons));
        },
      ),

      GoRoute(
        path: Routes.webPath,
        name: Routes.webPath,
        pageBuilder: (context, state) {
          dynamic url = (state.extra as Map<String, dynamic>?)?['url'];
          String? title = (state.extra as Map<String, dynamic>?)?['title'];
          return getPage(child: WebPage(url, title: title), state: state);
        },
      ),

      // ModalBottomSheetRoute(name: )
    ];

    router = GoRouter(
      navigatorKey: parentNavigatorKey,
      initialLocation: Routes.splashPath,
      debugLogDiagnostics: true,
      routes: routes,
      redirect: (context, state) {
        // if (UserStore.to.isUserLogin() || Routes.webPath == state.fullPath) {
        //   return null;
        // } else if (Routes.loginPath != state.fullPath && Routes.verificationPath != state.fullPath) {
        //   return Routes.loginPath;
        // }

        return null;
      },
    );
  }

  static Page getPage({
    required Widget child,
    required GoRouterState state,
  }) {
    return MaterialPage(
      key: state.pageKey,
      child: child,
    );
  }
}

class ModalPage<T> extends Page<T> {
  const ModalPage({required this.child, this.heightFraction});

  final Widget child;
  final double? heightFraction; // 屏幕高度的比例

  @override
  Route<T> createRoute(BuildContext context) => ModalBottomSheetRoute<T>(
        settings: this,
        // 这是关键属性，允许底部弹窗根据内容调整大小。
        isScrollControlled: true,
        // 让子部件自己处理背景色和圆角。
        backgroundColor: Colors.transparent,
        builder: (context) => child,
      );
  // @override
  // Route<T> createRoute(BuildContext context) => ModalBottomSheetRoute<T>(
  //       settings: this,
  //       isScrollControlled: true,
  //       showDragHandle: false,
  //       builder: (context) => DraggableScrollableSheet(
  //         builder: (context, scroller) {
  //           return child;
  //         },
  //         expand: false,
  //         initialChildSize: heightFraction ?? 0.5, // 使用传入的高度比例，默认为0.5
  //         minChildSize: 0.25, // 最小高度为屏幕的1/4
  //         maxChildSize: 0.95, // 最大高度为屏幕的95%
  //       ),
  //     );
}
