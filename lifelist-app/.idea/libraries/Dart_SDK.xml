<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/async" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/collection" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/convert" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/core" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/developer" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/html" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/io" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/isolate" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/math" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file:///Volumes/Disk0/fvm/versions/custom_3.22.0/bin/cache/dart-sdk/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>