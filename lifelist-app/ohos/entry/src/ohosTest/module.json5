{"module": {"name": "entry_test", "type": "feature", "description": "$string:module_test_desc", "mainElement": "TestAbility", "deviceTypes": ["phone"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:test_pages", "abilities": [{"name": "TestAbility", "srcEntry": "./ets/testability/TestAbility.ets", "description": "$string:TestAbility_desc", "icon": "$media:icon", "label": "$string:TestAbility_label", "exported": true, "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "skills": [{"actions": ["action.system.home"], "entities": ["entity.system.home"]}]}]}}