{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohos_MIv7RZV93KpmRdKph539PayU9uWFUtm-ud8gMdgIqL4=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001BEC12EBF48A333F585BD02C1F9AAD5E483A7621257163699F36A2984FB327CCE1877C27C2F9B52C",
          "profile": "/Users/<USER>/.ohos/config/default_ohos_MIv7RZV93KpmRdKph539PayU9uWFUtm-ud8gMdgIqL4=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohos_MIv7RZV93KpmRdKph539PayU9uWFUtm-ud8gMdgIqL4=.p12",
          "storePassword": "0000001B4A95AC709BA45510FC78924D428010368B214D6A0BB21ADC3A73B690180208D4ED668660185136"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "profile"
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}