import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/modules/list_tab/list_detail_page.dart';

void main() {
  group('ListDetailPage Tests', () {
    testWidgets('ListDetailPage should display loading initially', (WidgetTester tester) async {
      // 创建测试用的清单详情页面
      await tester.pumpWidget(
        MaterialApp(
          home: const ListDetailPage(listId: 'test-id'),
        ),
      );

      // 验证初始状态显示加载指示器
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('清单详情'), findsOneWidget);
    });

    testWidgets('ListDetailPage should display edit button in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ListDetailPage(listId: 'test-id'),
        ),
      );

      // 验证编辑按钮存在
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });
  });

  group('AchivementDetailModel Tests', () {
    test('AchivementDetailModel should parse JSON correctly', () {
      final jsonData = {
        'id': 1,
        'name': 'Test List',
        'description': 'Test Description',
        'cover_image': 'test.jpg',
        'completed_count': 3,
        'total_count': 10,
        'follow_count': 5,
        'progress': 0.3,
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T00:00:00',
        'items': [
          {
            'id': 1,
            'name': 'Test Item 1',
            'description': 'Test Item Description',
            'is_completed': true,
            'created_at': '2024-01-01T00:00:00',
            'updated_at': '2024-01-01T00:00:00',
          },
          {
            'id': 2,
            'name': 'Test Item 2',
            'description': 'Test Item Description 2',
            'is_completed': false,
            'created_at': '2024-01-01T00:00:00',
            'updated_at': '2024-01-01T00:00:00',
          }
        ]
      };

      final detail = AchivementDetailModel.fromJson(jsonData);

      expect(detail.id, equals(1));
      expect(detail.name, equals('Test List'));
      expect(detail.description, equals('Test Description'));
      expect(detail.coverImage, equals('test.jpg'));
      expect(detail.completedCount, equals(3));
      expect(detail.totalCount, equals(10));
      expect(detail.followCount, equals(5));
      expect(detail.progress, equals(0.3));
      expect(detail.items.length, equals(2));
      
      // 验证第一个子项
      expect(detail.items[0].id, equals(1));
      expect(detail.items[0].name, equals('Test Item 1'));
      expect(detail.items[0].isCompleted, equals(true));
      
      // 验证第二个子项
      expect(detail.items[1].id, equals(2));
      expect(detail.items[1].name, equals('Test Item 2'));
      expect(detail.items[1].isCompleted, equals(false));
    });

    test('AchivementDetailModel should handle empty items list', () {
      final jsonData = {
        'id': 1,
        'name': 'Empty List',
        'description': 'List with no items',
        'cover_image': null,
        'completed_count': 0,
        'total_count': 0,
        'follow_count': 0,
        'progress': 0.0,
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T00:00:00',
        'items': []
      };

      final detail = AchivementDetailModel.fromJson(jsonData);

      expect(detail.id, equals(1));
      expect(detail.name, equals('Empty List'));
      expect(detail.items.length, equals(0));
      expect(detail.progress, equals(0.0));
      expect(detail.completedCount, equals(0));
      expect(detail.totalCount, equals(0));
    });

    test('AchivementDetailModel should handle null cover image', () {
      final jsonData = {
        'id': 1,
        'name': 'Test List',
        'description': 'Test Description',
        'cover_image': null,
        'completed_count': 5,
        'total_count': 10,
        'follow_count': 2,
        'progress': 0.5,
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T00:00:00',
        'items': []
      };

      final detail = AchivementDetailModel.fromJson(jsonData);

      expect(detail.coverImage, isNull);
      expect(detail.name, equals('Test List'));
      expect(detail.progress, equals(0.5));
    });
  });
}
