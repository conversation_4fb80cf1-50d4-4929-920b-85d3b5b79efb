import 'package:flutter_test/flutter_test.dart';
import 'package:lifelist/common/base_model.dart';
import 'package:lifelist/models/list_model.dart';
import 'package:lifelist/models/user_model.dart';

void main() {
  group('BaseModel Tests', () {
    test('BaseModel should parse success response correctly', () {
      final jsonData = {
        'code': 1,
        'msg': 'Success',
        'serial_number': '1234567890',
        'data': {
          'id': '1',
          'uuid': 'test-uuid',
          'nickname': 'Test User',
          'avatar': null,
          'birthdate': null,
          'is_vip': false,
          'token': 'test-token',
          'created_at': '2024-01-01T00:00:00',
          'updated_at': '2024-01-01T00:00:00',
        }
      };

      final baseModel = BaseModel<UserModel>.fromJson(
        jsonData,
        (json) => UserModel.fromJson(json as Map<String, dynamic>),
      );

      expect(baseModel.code, equals(1));
      expect(baseModel.msg, equals('Success'));
      expect(baseModel.serialNumber, equals('1234567890'));
      expect(baseModel.data, isNotNull);
      expect(baseModel.data!.nickname, equals('Test User'));
      expect(baseModel.data!.token, equals('test-token'));
    });

    test('BaseModel should parse error response correctly', () {
      final jsonData = {'code': 0, 'msg': 'Error occurred', 'serial_number': '1234567890', 'data': null};

      final baseModel = BaseModel<UserModel>.fromJson(
        jsonData,
        (json) => UserModel.fromJson(json as Map<String, dynamic>),
      );

      expect(baseModel.code, equals(0));
      expect(baseModel.msg, equals('Error occurred'));
      expect(baseModel.serialNumber, equals('1234567890'));
      expect(baseModel.data, isNull);
    });

    test('BaseModel should parse list response correctly', () {
      final jsonData = {
        'code': 1,
        'msg': 'Success',
        'serial_number': '1234567890',
        'data': {
          'list': [
            {
              'id': '1',
              'name': 'Test List',
              'description': 'Test Description',
              'cover_image': 'test.jpg',
              'completed_count': 3,
              'total_count': 10,
              'follow_count': 5,
              'progress': 0.3,
              'created_at': '2024-01-01T00:00:00',
              'updated_at': '2024-01-01T00:00:00',
            }
          ],
          'pagination': {'page': 1, 'per_page': 20, 'total': 1, 'pages': 1, 'has_next': false, 'has_prev': false}
        }
      };

      final baseModel = BaseModel<List<AchivementModel>>.fromJson(
        jsonData,
        (json) {
          if (json is Map<String, dynamic> && json.containsKey('list')) {
            final list = json['list'] as List<dynamic>;
            return list.map((item) => AchivementModel.fromJson(item)).toList();
          }
          return <AchivementModel>[];
        },
      );

      expect(baseModel.code, equals(1));
      expect(baseModel.msg, equals('Success'));
      expect(baseModel.data, isNotNull);
      expect(baseModel.data!.length, equals(1));
      expect(baseModel.data!.first.name, equals('Test List'));
    });
  });

  group('Model Tests', () {
    test('UserModel should parse JSON correctly', () {
      final jsonData = {
        'id': '1',
        'uuid': 'test-uuid',
        'nickname': 'Test User',
        'avatar': 'test-avatar.jpg',
        'birthdate': '1990-01-01',
        'is_vip': true,
        'token': 'test-token',
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T00:00:00',
      };

      final user = UserModel.fromJson(jsonData);

      expect(user.id, equals('1'));
      expect(user.uuid, equals('test-uuid'));
      expect(user.nickname, equals('Test User'));
      expect(user.avatar, equals('test-avatar.jpg'));
      expect(user.birthdate, equals('1990-01-01'));
      expect(user.isVip, equals(true));
      expect(user.token, equals('test-token'));
    });

    test('AchivementModel should parse JSON correctly', () {
      final jsonData = {
        'id': '1',
        'name': 'Test Achievement',
        'description': 'Test Description',
        'cover_image': 'test.jpg',
        'completed_count': 5,
        'total_count': 10,
        'follow_count': 3,
        'progress': 0.5,
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T00:00:00',
      };

      final achievement = AchivementModel.fromJson(jsonData);

      expect(achievement.id, equals('1'));
      expect(achievement.name, equals('Test Achievement'));
      expect(achievement.description, equals('Test Description'));
      expect(achievement.completedCount, equals(5));
      expect(achievement.totalCount, equals(10));
      // expect(achievement.followCount, equals(3)); // followCount属性已移除
      expect(achievement.progress, equals(0.5));
      expect(achievement.progressText, equals('5/10'));
    });
  });
}
