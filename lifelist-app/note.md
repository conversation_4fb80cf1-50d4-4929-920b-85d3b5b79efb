# 《美瞬人生清单》App 项目文档

---

## 项目概述

**项目名称**：美瞬人生清单  
**项目目标**：帮助用户记录和追踪人生中的各类“已完成清单”（如“我去过的城市”、“看过的电影”、“和另一半一起完成的100件事”等），通过社交化推荐与个性化管理，提升用户的生活仪式感与成就感。

**技术栈**：
- **前端（App）**：Flutter（支持 iOS & Android）
- **后端（Server）**：Python + Flask
- **数据库**：PostgreSQL（推荐）或 MySQL
- **用户认证**：华为 HMS Core OAuth2 登录（可选，非强制登录）
- **部署**：可部署于云服务器（如华为云、阿里云、AWS等）

**项目目录**
```
lifelist-server/
├── app.py
├── config.py
├── models/
│   ├── user.py
│   ├── list.py
│   └── item.py
├── routes/
│   ├── auth.py
│   ├── lists.py
│   └── items.py
├── services/
│   └── huawei_auth.py
└── requirements.txt

lifelist-app/
├── lib/
│   ├── main.dart
│   ├── constants/
│   │   ├── colors.dart
│   │   └── ...
│   ├── screens/
│   │   ├── splash_screen.dart
│   │   ├── login_screen.dart
│   │   ├── home_screen.dart
│   │   ├── lists_screen.dart
|   │   ├── create_list_screen.dart
|   │   ├── edit_list_screen.dart
|   │   ├── add_item_screen.dart
|   │   ├── edit_item_screen.dart
|   │   ├── share_list_screen.dart
|   │   ├── follow_list_screen.dart
|   │   ├── unfollow_list_screen.dart
|   │   ├── delete_list_screen.dart
|   │   ├── delete_item_screen.dart
|   │   ├── complete_item_screen.dart
|   │   ├── incomplete_item_screen.dart
|   │   ├── sort_item_screen.dart
|   │   ├── upload_cover_screen.dart
|   │   ├── generate_share_link_screen.dart
|   │   ├── generate_share_image_screen.dart
|   │   ├── copy_share_link_screen.dart
|   │   ├── copy_share_image_screen.dart
|   │   ├── vip_screen.dart
|   │   ├── profile_screen.dart
│   │   ├── list_detail_screen.dart
│   │   └── ...
│   ├── blocs/
│   ├── widgets/
│   └── services/
│       └── api_client.dart
│       └── huawei_auth_service.dart
│   └── ...
├── assets/
│   ├── images/
│   ├── fonts/
│   └── ...
└── pubspec.yaml
```
---

## 功能概览

| 功能模块 | 描述 |
|--------|------|
| 首页（Home） | 展示热门推荐清单（无需登录即可查看） |
| 清单（My Lists） | 展示用户关注或创建的清单，支持增删改查 |
| 我的（Profile） | 用户信息、设置、隐私政策、分享等 |
| 清单详情页 | 查看清单内容、完成状态、编辑、分享、上传背景图 |
| 用户系统 | 支持华为登录（OAuth2），支持游客模式 |
| VIP 功能 | 免费用户最多创建 5 个清单，VIP 用户无限制 |

---

## App 端设计（Flutter）

### 技术选型
1. Flutter 3.22.0
2. 状态管理: flutter_bloc
3. 路由管理：go_router
4. 网络请求：dio
5. UI 框架：Cupertino（iOS 风格）
6. 图片处理：flutter_image_compress

### 页面结构（底部 Tab）

1. **首页（Home）**
2. **清单（My Lists）**
3. **我的（Profile）**

---

### 1. 首页（Home）

- 展示热门清单卡片（如“我去过的中国城市”、“豆瓣Top250电影”、“情侣100件小事”等）
- 每个卡片包含：
  - 封面图
  - 清单标题
  - 描述
  - 已完成人数 / 关注数
  - “Follow”按钮（点击后加入“我的清单”）
- 支持下拉刷新、上拉加载更多
- 点击卡片跳转至 **清单详情页**（只读模式）

---

### 2. 清单（My Lists）

- 展示用户已关注或创建的所有清单
- 列表项包含：
  - 清单封面图（默认背景图）
  - 清单名称
  - 已完成 / 总子项数（如 `12/30`）
  - 进度条（百分比）
  - 已完成项绿色，未完成项灰色
- 点击进入 **清单详情页**
- 支持右上角“+”按钮创建新清单（免费用户最多5个）

---

### 3. 我的（Profile）

- 用户头像（支持华为登录头像，游客默认头像）
- 昵称（可编辑）
- 出生日期（用于年龄计算，可选）
- 设置项：
  - 隐私政策
  - 关于我们
  - 分享 App（生成分享链接）
  - 登录/登出（华为登录入口）
  - VIP 开通入口（跳转至支付页面或说明页）
- 游客模式：使用本地 UUID 标识用户，数据可同步（登录后合并）

---

### 4. 清单详情页

- 展示清单所有子项
- 子项支持：
  - 点击切换完成状态（✔️ / ○）
  - 长按编辑名称
  - 拖动排序（可选）
- 功能按钮：
  - 编辑清单（修改名称、描述、封面图）
  - 添加子项
  - 删除子项
  - 分享清单（生成链接或图片）
- 顶部展示：
  - 清单封面图（可上传）
  - 名称
  - 进度条
  - 关注人数 / 创建者信息

---

## 后端设计（Flask）

### 技术选型
1. Python 3.10
2. Flask 2.2.2
3. 数据库：MySQL
4. ORM：SQLAlchemy
5. 鉴权：JWT

### 1. API 接口设计

| 路径 | 方法 | 描述 |
|------|------|------|
| `/api/v1/lists/recommended` | GET | 获取推荐热门清单列表 |
| `/api/v1/lists/my` | GET | 获取用户自己的清单列表（需鉴权） |
| `/api/v1/lists` | POST | 创建新清单（需鉴权） |
| `/api/v1/lists/<id>` | GET | 获取清单详情 |
| `/api/v1/lists/<id>` | PUT | 更新清单（名称、描述、封面图） |
| `/api/v1/lists/<id>` | DELETE | 删除清单 |
| `/api/v1/lists/<id>/follow` | POST | 关注清单 |
| `/api/v1/lists/<id>/unfollow` | POST | 取消关注 |
| `/api/v1/items` | POST | 添加子项 |
| `/api/v1/items/<id>` | PUT | 修改子项名称或完成状态 |
| `/api/v1/items/<id>` | DELETE | 删除子项 |
| `/api/v1/auth/huawei` | POST | 华为 OAuth2 登录回调处理 |
| `/api/v1/user/profile` | GET | 获取用户信息 |
| `/api/v1/user/profile` | PUT | 更新用户信息（昵称、出生日期等） |
| `/api/v1/user/vip` | GET | 查询用户 VIP 状态 |

---

### 2. 华为登录（OAuth2）集成

#### 流程：

1. App 端调用华为 HMS Core 登录 SDK
2. 获取 `access_token` 和 `openid`
3. 发送至后端 `/api/v1/auth/huawei` 接口
4. 后端验证 token 并获取用户信息（头像、昵称）
5. 创建或更新本地用户，返回 JWT token

#### 请求示例：

```json
POST /api/v1/auth/huawei
{
  "access_token": "xxx",
  "openid": "yyy"
}
```

#### 响应
```json
{
  "user_id": "u_123",
  "nickname": "张三",
  "avatar": "https://...",
  "is_vip": false,
  "token": "jwt_token_string"
}
```

## 数据库表结构设计

### 1. 用户表（User）

| 字段名 | 类型 | 描述 |
|------|------|------|
| id | int | 主键，自增 |
| uuid | varchar(36) | 唯一标识符，用于游客模式 |
| nickname | varchar(50) | 昵称 |
| avatar | varchar(255) | 头像 URL |
| birthdate | date | 出生日期，可选 |
| is_vip | boolean | 是否为 VIP 用户 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 2. 清单表（List）

| 字段名 | 类型 | 描述 |
|------|------|------|
| id | int | 主键，自增 |
| user_id | int | 创建者用户 ID |
| name | varchar(100) | 清单名称 |
| description | text | 描述 |
| cover_image | varchar(255) | 封面图 URL |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 3. 子项表（Item）

| 字段名 | 类型 | 描述 |
|------|------|------|
| id | int | 主键，自增 |
| list_id | int | 所属清单 ID |
| name | varchar(100) | 子项名称 |
| is_completed | boolean | 是否已完成 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### ...