name: lifelist
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.1+10001

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  logger: ^2.3.0
  permission_handler: 
    git:
      url: https://gitcode.com/openharmony-sig/flutter_permission_handler.git
      path: permission_handler
      ref: br_permission_handler_v11.3.1_ohos
  cached_network_image: ^3.4.1
  flutter_easyloading: ^3.0.5
  package_info_plus: 
    git:
      url: https://gitcode.com/openharmony-sig/flutter_plus_plugins.git
      path: packages/package_info_plus/package_info_plus
      ref: br_package_info_plus-v8.1.0_ohos
  fluttertoast: 
    git:
      url: https://gitcode.com/openharmony-sig/flutter_fluttertoast.git
      ref: br_8.2.8_ohos
  dio: ^5.4.3+1
  json_annotation: ^4.9.0
  sign_in_with_huawei: ^0.0.3
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5
  url_launcher: 
    git:
      url: "https://gitcode.com/openharmony-tpc/flutter_packages.git"
      path: "packages/url_launcher/url_launcher"
  shared_preferences:
    git:
      url: "https://gitcode.com/openharmony-tpc/flutter_packages.git"
      path: "packages/shared_preferences/shared_preferences"
  intl: 0.19.0
  flutter_spinkit: 5.2.1
  go_router: ^14.1.4
  flutter_smart_dialog: ^4.9.7+4
  flutter_screenutil: ^5.9.3
  image_picker: 
    git: 
      url: "https://gitcode.com/openharmony-tpc/flutter_packages.git"
      path: "packages/image_picker/image_picker"
  flutter_slidable: ^3.1.1
  flutter_inappwebview: 
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_inappwebview"
      path: "flutter_inappwebview"
  in_app_purchase: ^3.2.0
  # in_app_purchase: 
  #   git: 
  #     url: "https://gitcode.com/openharmony-tpc/flutter_packages.git"
  #     path: "packages/in_app_purchase/in_app_purchase"

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

  flutter_localizations:
    sdk: flutter  # 添加这一行

  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

dev_dependencies:
  build_runner:
  custom_lint:
  retrofit_generator: '>=8.0.0 <10.0.0'
  json_serializable: ^6.8.0
  bloc_test: ^10.0.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
