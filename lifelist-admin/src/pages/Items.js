import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, message, Space } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { apiService } from '../services/apiService';
import dayjs from 'dayjs';

const Items = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    loadItems();
  }, []);

  const loadItems = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      const response = await apiService.getItems(page, pageSize);
      if (response.success) {
        setItems(response.data);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: response.pagination.total,
        });
      }
    } catch (error) {
      message.error('加载子项数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (paginationInfo) => {
    loadItems(paginationInfo.current, paginationInfo.pageSize);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '子项名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '所属清单',
      dataIndex: 'list_name',
      key: 'list_name',
      ellipsis: true,
    },
    {
      title: '清单创建者',
      dataIndex: 'list_creator',
      key: 'list_creator',
    },
    {
      title: '完成状态',
      dataIndex: 'is_completed',
      key: 'is_completed',
      render: (isCompleted) => (
        <Tag 
          color={isCompleted ? 'success' : 'default'} 
          icon={isCompleted ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
        >
          {isCompleted ? '已完成' : '未完成'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h1>子项管理</h1>
        <Button onClick={() => loadItems(pagination.current, pagination.pageSize)}>
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={items}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default Items;
