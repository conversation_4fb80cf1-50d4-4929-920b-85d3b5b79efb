import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, message, Popconfirm, Space, Progress } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, DeleteOutlined } from '@ant-design/icons';
import { apiService } from '../services/apiService';
import dayjs from 'dayjs';

const Lists = () => {
  const [lists, setLists] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    loadLists();
  }, []);

  const loadLists = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      const response = await apiService.getLists(page, pageSize);
      if (response.success) {
        setLists(response.data);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: response.pagination.total,
        });
      }
    } catch (error) {
      message.error('加载清单数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePublic = async (listId) => {
    try {
      const response = await apiService.toggleListPublic(listId);
      if (response.success) {
        message.success(response.message || '公开状态切换成功');
        loadLists(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleDelete = async (listId) => {
    try {
      const response = await apiService.deleteList(listId);
      if (response.success) {
        message.success(response.message || '删除成功');
        loadLists(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleTableChange = (paginationInfo) => {
    loadLists(paginationInfo.current, paginationInfo.pageSize);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '清单名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
    },
    {
      title: '公开状态',
      dataIndex: 'is_public',
      key: 'is_public',
      render: (isPublic) => (
        <Tag color={isPublic ? 'green' : 'red'} icon={isPublic ? <EyeOutlined /> : <EyeInvisibleOutlined />}>
          {isPublic ? '公开' : '私有'}
        </Tag>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      render: (_, record) => {
        const percent = record.total_items > 0 ? Math.round((record.completed_items / record.total_items) * 100) : 0;
        return (
          <div style={{ width: 120 }}>
            <Progress
              percent={percent}
              size="small"
              format={() => `${record.completed_items}/${record.total_items}`}
            />
          </div>
        );
      },
    },
    {
      title: '关注数',
      dataIndex: 'follows',
      key: 'follows',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Popconfirm
            title={`确定要${record.is_public ? '设为私有' : '设为公开'}吗？`}
            onConfirm={() => handleTogglePublic(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small">
              {record.is_public ? '设为私有' : '设为公开'}
            </Button>
          </Popconfirm>
          <Popconfirm
            title="确定要删除这个清单吗？此操作不可恢复！"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h1>清单管理</h1>
        <Button onClick={() => loadLists(pagination.current, pagination.pageSize)}>
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={lists}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default Lists;
