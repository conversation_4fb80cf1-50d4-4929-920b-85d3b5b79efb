import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, message } from 'antd';
import {
  UserOutlined,
  CrownOutlined,
  UnorderedListOutlined,
  FileTextOutlined,
  HeartOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { apiService } from '../services/apiService';

const Dashboard = () => {
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const data = await apiService.getStats();
      setStats(data);
    } catch (error) {
      message.error('加载统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (type) => {
    try {
      let blob;
      let filename;
      
      switch (type) {
        case 'users':
          blob = await apiService.exportUsers();
          filename = `users_${new Date().toISOString().slice(0, 10)}.csv`;
          break;
        case 'lists':
          blob = await apiService.exportLists();
          filename = `lists_${new Date().toISOString().slice(0, 10)}.csv`;
          break;
        case 'items':
          blob = await apiService.exportItems();
          filename = `items_${new Date().toISOString().slice(0, 10)}.csv`;
          break;
        default:
          return;
      }

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  return (
    <div>
      <h1>仪表板</h1>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card>
            <Statistic
              title="VIP用户"
              value={stats.vipUsers}
              prefix={<CrownOutlined />}
              loading={loading}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card>
            <Statistic
              title="总清单数"
              value={stats.totalLists}
              prefix={<UnorderedListOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card>
            <Statistic
              title="总子项数"
              value={stats.totalItems}
              prefix={<FileTextOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card>
            <Statistic
              title="总关注数"
              value={stats.totalFollows}
              prefix={<HeartOutlined />}
              loading={loading}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="数据导出" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => handleExport('users')}
            >
              导出用户数据
            </Button>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => handleExport('lists')}
            >
              导出清单数据
            </Button>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => handleExport('items')}
            >
              导出子项数据
            </Button>
          </Col>
        </Row>
      </Card>

      <Card title="系统信息">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <p><strong>应用名称：</strong>美瞬人生清单</p>
            <p><strong>版本：</strong>1.0.0</p>
            <p><strong>环境：</strong>开发环境</p>
          </Col>
          <Col span={12}>
            <p><strong>数据库：</strong>SQLite</p>
            <p><strong>服务器：</strong>Flask</p>
            <p><strong>前端：</strong>React + Ant Design</p>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Dashboard;
