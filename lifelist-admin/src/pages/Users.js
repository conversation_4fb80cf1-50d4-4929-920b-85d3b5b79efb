import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, message, Popconfirm, Space } from 'antd';
import { CrownOutlined, UserOutlined } from '@ant-design/icons';
import { apiService } from '../services/apiService';
import dayjs from 'dayjs';

const Users = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      const response = await apiService.getUsers(page, pageSize);
      if (response.success) {
        setUsers(response.data);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: response.pagination.total,
        });
      }
    } catch (error) {
      message.error('加载用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleVip = async (userId) => {
    try {
      const response = await apiService.toggleUserVip(userId);
      if (response.success) {
        message.success(response.message || 'VIP状态切换成功');
        loadUsers(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleTableChange = (paginationInfo) => {
    loadUsers(paginationInfo.current, paginationInfo.pageSize);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      render: (text, record) => (
        <Space>
          {record.is_vip ? <CrownOutlined style={{ color: '#faad14' }} /> : <UserOutlined />}
          {text}
        </Space>
      ),
    },
    {
      title: 'UUID',
      dataIndex: 'uuid',
      key: 'uuid',
      ellipsis: true,
    },
    {
      title: 'VIP状态',
      dataIndex: 'is_vip',
      key: 'is_vip',
      render: (isVip) => (
        <Tag color={isVip ? 'gold' : 'default'}>
          {isVip ? 'VIP' : '普通用户'}
        </Tag>
      ),
    },
    {
      title: '清单数量',
      dataIndex: 'list_count',
      key: 'list_count',
      width: 100,
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Popconfirm
            title={`确定要${record.is_vip ? '取消' : '设置'}VIP状态吗？`}
            onConfirm={() => handleToggleVip(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small">
              {record.is_vip ? '取消VIP' : '设为VIP'}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h1>用户管理</h1>
        <Button onClick={() => loadUsers(pagination.current, pagination.pageSize)}>
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default Users;
