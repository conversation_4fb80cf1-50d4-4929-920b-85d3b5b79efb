import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8002';

class AuthService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
    });

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('admin_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('admin_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async login(username, password) {
    try {
      // 这里应该调用后端的管理员登录API
      // 暂时模拟登录
      if (username === 'admin' && password === 'admin123') {
        const token = 'mock_admin_token_' + Date.now();
        return { success: true, token };
      } else {
        throw new Error('用户名或密码错误');
      }
    } catch (error) {
      throw error;
    }
  }

  logout() {
    localStorage.removeItem('admin_token');
  }

  isAuthenticated() {
    return !!localStorage.getItem('admin_token');
  }

  getToken() {
    return localStorage.getItem('admin_token');
  }
}

export const authService = new AuthService();
