import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8002';

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
    });

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('admin_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  // 获取统计数据
  async getStats() {
    try {
      // 模拟数据，实际应该调用后端API
      return {
        totalUsers: 1250,
        vipUsers: 89,
        totalLists: 3420,
        totalItems: 15680,
        totalFollows: 8920,
      };
    } catch (error) {
      throw error;
    }
  }

  // 用户管理
  async getUsers(page = 1, pageSize = 20) {
    try {
      const response = await this.api.get('/api/v1/admin/users', {
        params: { page, page_size: pageSize }
      });
      return response.data;
    } catch (error) {
      // 模拟数据
      return {
        success: true,
        data: [
          {
            id: 1,
            nickname: '示例用户',
            uuid: 'uuid-1234',
            is_vip: false,
            list_count: 3,
            created_at: '2024-01-01T00:00:00',
          },
          {
            id: 2,
            nickname: 'VIP用户',
            uuid: 'uuid-5678',
            is_vip: true,
            list_count: 15,
            created_at: '2024-01-02T00:00:00',
          },
        ],
        pagination: {
          page: 1,
          total: 2,
          pages: 1,
        }
      };
    }
  }

  async toggleUserVip(userId) {
    try {
      const response = await this.api.post(`/api/v1/admin/users/${userId}/toggle-vip`);
      return response.data;
    } catch (error) {
      // 模拟成功
      return { success: true, message: 'VIP状态切换成功' };
    }
  }

  // 清单管理
  async getLists(page = 1, pageSize = 20) {
    try {
      const response = await this.api.get('/api/v1/admin/lists', {
        params: { page, page_size: pageSize }
      });
      return response.data;
    } catch (error) {
      // 模拟数据
      return {
        success: true,
        data: [
          {
            id: 1,
            name: '我去过的中国城市',
            creator: '示例用户',
            is_public: true,
            total_items: 10,
            completed_items: 3,
            follows: 25,
            created_at: '2024-01-01T00:00:00',
          },
          {
            id: 2,
            name: '豆瓣Top250电影',
            creator: 'VIP用户',
            is_public: true,
            total_items: 250,
            completed_items: 45,
            follows: 156,
            created_at: '2024-01-02T00:00:00',
          },
        ],
        pagination: {
          page: 1,
          total: 2,
          pages: 1,
        }
      };
    }
  }

  async toggleListPublic(listId) {
    try {
      const response = await this.api.post(`/api/v1/admin/lists/${listId}/toggle-public`);
      return response.data;
    } catch (error) {
      return { success: true, message: '公开状态切换成功' };
    }
  }

  async deleteList(listId) {
    try {
      const response = await this.api.delete(`/api/v1/admin/lists/${listId}`);
      return response.data;
    } catch (error) {
      return { success: true, message: '清单删除成功' };
    }
  }

  // 子项管理
  async getItems(page = 1, pageSize = 20) {
    try {
      const response = await this.api.get('/api/v1/admin/items', {
        params: { page, page_size: pageSize }
      });
      return response.data;
    } catch (error) {
      // 模拟数据
      return {
        success: true,
        data: [
          {
            id: 1,
            name: '北京',
            list_name: '我去过的中国城市',
            list_creator: '示例用户',
            is_completed: true,
            created_at: '2024-01-01T00:00:00',
          },
          {
            id: 2,
            name: '肖申克的救赎',
            list_name: '豆瓣Top250电影',
            list_creator: 'VIP用户',
            is_completed: false,
            created_at: '2024-01-02T00:00:00',
          },
        ],
        pagination: {
          page: 1,
          total: 2,
          pages: 1,
        }
      };
    }
  }

  // 导出功能
  async exportUsers() {
    try {
      const response = await this.api.get('/api/v1/admin/export/users', {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async exportLists() {
    try {
      const response = await this.api.get('/api/v1/admin/export/lists', {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async exportItems() {
    try {
      const response = await this.api.get('/api/v1/admin/export/items', {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

export const apiService = new ApiService();
