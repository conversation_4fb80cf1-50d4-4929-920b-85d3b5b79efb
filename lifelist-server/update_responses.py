#!/usr/bin/env python3
"""
批量更新API响应格式的脚本
"""
import os
import re

def update_file_responses(file_path):
    """更新单个文件的响应格式"""
    if not os.path.exists(file_path):
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入语句
    if 'from utils.response import' not in content:
        # 找到其他导入语句的位置
        import_pattern = r'(from flask import.*?\n)'
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r'\1from utils.response import success_response, error_response, paginated_response\n',
                content,
                count=1
            )
    
    # 替换成功响应
    # 替换 return jsonify({'success': True, 'data': xxx})
    content = re.sub(
        r'return jsonify\(\{\s*[\'"]success[\'"]\s*:\s*True,\s*[\'"]data[\'"]\s*:\s*([^}]+)\s*\}\)',
        r'return success_response(data=\1)',
        content
    )
    
    # 替换 return jsonify({'success': True, 'message': xxx})
    content = re.sub(
        r'return jsonify\(\{\s*[\'"]success[\'"]\s*:\s*True,\s*[\'"]message[\'"]\s*:\s*([^}]+)\s*\}\)',
        r'return success_response(msg=\1)',
        content
    )
    
    # 替换错误响应
    # 替换 return jsonify({'success': False, 'message': xxx}), status_code
    content = re.sub(
        r'return jsonify\(\{\s*[\'"]success[\'"]\s*:\s*False,\s*[\'"]message[\'"]\s*:\s*([^}]+)\s*\}\),\s*(\d+)',
        r'return error_response(msg=\1), \2',
        content
    )
    
    # 替换 return jsonify({'success': False, 'message': xxx})
    content = re.sub(
        r'return jsonify\(\{\s*[\'"]success[\'"]\s*:\s*False,\s*[\'"]message[\'"]\s*:\s*([^}]+)\s*\}\)',
        r'return error_response(msg=\1)',
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Updated: {file_path}")

def main():
    """主函数"""
    # 需要更新的文件列表
    files_to_update = [
        'routes/lists.py',
        'routes/items.py',
        'routes/users.py',
    ]
    
    for file_path in files_to_update:
        update_file_responses(file_path)
    
    print("All files updated successfully!")

if __name__ == '__main__':
    main()
