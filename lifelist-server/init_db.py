"""
初始化数据库
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

def init_database():
    """初始化数据库"""
    app = create_app()
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("数据库表创建完成")
        
        # 执行迁移
        try:
            # 添加新字段
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    ALTER TABLE lists
                    ADD COLUMN is_shared_to_square BOOLEAN DEFAULT FALSE NOT NULL
                """))
                conn.commit()
            print("添加 is_shared_to_square 字段成功")
        except Exception as e:
            print(f"添加 is_shared_to_square 字段失败或已存在: {e}")

        try:
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    ALTER TABLE lists
                    ADD COLUMN original_list_id INTEGER
                """))
                conn.commit()
            print("添加 original_list_id 字段成功")
        except Exception as e:
            print(f"添加 original_list_id 字段失败或已存在: {e}")
        
        print("数据库初始化完成")

if __name__ == '__main__':
    init_database()
