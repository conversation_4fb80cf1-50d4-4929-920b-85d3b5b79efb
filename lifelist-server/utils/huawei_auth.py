import json
import base64
import requests
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicNumbers
from cryptography.hazmat.primitives.serialization import load_pem_public_key
from cryptography.hazmat.backends import default_backend
import jwt
from typing import Dict, Optional, Any
import time


class IDTokenParser:
    # 配置常量
    CLIENT_ID = "6917581834908375826"  # 请替换为您的Client ID
    MAX_PUBLIC_KEY_SIZE = 4

    # JWK端点
    CERT_URL = "https://oauth-login.cloud.huawei.com/oauth2/v3/certs"

    # ID Token的issuer
    ID_TOKEN_ISSUE = "https://accounts.huawei.com"
    ALG_RS256 = "RS256"
    ALG_PS256 = "PS256"

    def __init__(self):
        # 缓存jwt公钥信息
        self.key_id_to_public_key = {}
        self.jwks_cache = None
        self.cache_time = None
        self.cache_duration = 3600  # 缓存1小时

    @staticmethod
    def base64url_to_int(b64url: str) -> int:
        """将 base64url 编码转换为整数"""
        # 补齐填充
        padding = '=' * (4 - len(b64url) % 4) if len(b64url) % 4 != 0 else ''
        binary = base64.urlsafe_b64decode(b64url + padding)
        return int.from_bytes(binary, 'big')

    def get_rsa_public_key_by_jwk(self, jwk_object: Dict[str, Any]) -> bytes:
        """
        通过jwk获取公钥信息
        """
        try:
            # 从 JWK 提取参数
            n = self.base64url_to_int(jwk_object['n'])  # modulus
            e = self.base64url_to_int(jwk_object['e'])  # public exponent

            # 创建公钥对象
            public_numbers = RSAPublicNumbers(e=e, n=n)
            public_key = public_numbers.public_key()

            # 转换为 PEM 格式
            pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.PKCS1
            )

            return pem

        except Exception as e:
            raise Exception(f"获取公钥失败: {str(e)}")

    def get_jwks(self) -> Optional[Dict]:
        """
        从华为获取jwt公钥信息jwk
        因为jwk每天都会更新，所以需要缓存jwk
        """
        # 检查缓存
        current_time = time.time()
        if (self.jwks_cache and
                self.cache_time and
                current_time - self.cache_time < self.cache_duration):
            return self.jwks_cache

        try:
            response = requests.get(
                self.CERT_URL,
                timeout=5,
                headers={'User-Agent': 'Python-IDTokenParser/1.0'}
            )
            response.raise_for_status()

            jwks = response.json()
            self.jwks_cache = jwks
            self.cache_time = current_time

            return jwks

        except Exception as e:
            print(f"获取JWKS失败: {str(e)}")
            # 如果有缓存，返回缓存数据
            if self.jwks_cache:
                return self.jwks_cache
            return None

    def get_rsa_public_key_by_kid(self, key_id: str) -> Optional[bytes]:
        """
        通过kid获取公钥信息，请缓存公钥信息
        """
        # 检查缓存
        if key_id in self.key_id_to_public_key:
            return self.key_id_to_public_key[key_id]

        # 获取JWKS
        jwks = self.get_jwks()
        if not jwks or 'keys' not in jwks:
            return None

        # 清理缓存如果超过最大大小
        if len(self.key_id_to_public_key) > self.MAX_PUBLIC_KEY_SIZE:
            self.key_id_to_public_key.clear()

        # 查找对应的密钥
        for key in jwks['keys']:
            kid = key.get('kid')
            alg = key.get('alg')

            if alg in [self.ALG_RS256, self.ALG_PS256]:
                try:
                    public_key_pem = self.get_rsa_public_key_by_jwk(key)
                    self.key_id_to_public_key[kid] = public_key_pem
                except Exception as e:
                    print(f"处理密钥 {kid} 失败: {str(e)}")
                    continue

        return self.key_id_to_public_key.get(key_id)

    def ps256_verify(self, header: str, payload: str, signature: str, public_key_pem: bytes) -> bool:
        """
        PS256算法验证签名
        """
        try:
            # 构造待验证的数据
            content = f"{header}.{payload}".encode('utf-8')

            # 解码签名
            signature_bytes = base64.urlsafe_b64decode(
                signature + '=' * (4 - len(signature) % 4)
            )

            # 加载公钥
            public_key = load_pem_public_key(public_key_pem, backend=default_backend())

            # 验证签名 (使用PSS填充)
            public_key.verify(
                signature_bytes,
                content,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )

            return True

        except Exception as e:
            print(f"PS256验证失败: {str(e)}")
            return False

    def verify_and_parse(self, id_token: str) -> Optional[Dict]:
        """
        验证并解析ID Token
        """
        try:
            # 解码JWT头部和载荷（不验证签名）
            header_data, payload_data, signature = id_token.split('.')

            # 解码头部获取算法和kid
            header_json = base64.urlsafe_b64decode(
                header_data + '=' * (4 - len(header_data) % 4)
            )
            header = json.loads(header_json)

            # 解码载荷
            payload_json = base64.urlsafe_b64decode(
                payload_data + '=' * (4 - len(payload_data) % 4)
            )
            payload = json.loads(payload_json)

            # issuer校验
            if payload.get('iss') != self.ID_TOKEN_ISSUE:
                raise Exception("issuer不匹配")

            # audience校验
            audience = payload.get('aud')
            if audience and audience != self.CLIENT_ID:
                raise Exception("audience不匹配")

            # 获取算法和kid
            alg = header.get('alg')
            kid = header.get('kid')

            if not kid:
                raise Exception("Token头部缺少kid")

            # 获取公钥
            public_key_pem = self.get_rsa_public_key_by_kid(kid)
            if not public_key_pem:
                raise Exception("未找到对应的公钥")

            # 根据算法类型进行验证
            if alg == self.ALG_RS256:
                # RS256验证
                decoded_jwt = jwt.decode(
                    id_token,
                    public_key_pem,
                    algorithms=[self.ALG_RS256],
                    options={
                        'verify_signature': True,
                        'verify_exp': True,
                        'verify_nbf': True,
                        'verify_iat': True,
                        'verify_aud': False,  # 我们已经手动验证了
                        'verify_iss': False  # 我们已经手动验证了
                    }
                )

                # 添加额外信息
                decoded_jwt['alg'] = alg
                decoded_jwt['typ'] = header.get('typ')
                decoded_jwt['kid'] = kid

                return decoded_jwt

            elif alg == self.ALG_PS256:
                # PS256验证
                is_valid = self.ps256_verify(header_data, payload_data, signature, public_key_pem)
                if is_valid:
                    # 手动添加额外信息
                    payload['alg'] = alg
                    payload['typ'] = header.get('typ')
                    payload['kid'] = kid
                    return payload
                else:
                    raise Exception("PS256签名验证失败")

            else:
                raise Exception(f"不支持的算法: {alg}")

        except jwt.ExpiredSignatureError:
            raise Exception("ID Token已过期")
        except jwt.InvalidTokenError as e:
            raise Exception(f"ID Token无效: {str(e)}")
        except Exception as e:
            raise Exception(f"ID Token解析失败: {str(e)}")
