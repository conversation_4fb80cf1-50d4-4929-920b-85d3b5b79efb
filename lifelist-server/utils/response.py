"""
统一响应格式工具
"""
import time
from flask import jsonify

def success_response(data=None, msg="Success", code=1):
    """
    成功响应格式
    
    Args:
        data: 响应数据
        msg: 响应消息
        code: 响应码，1表示成功
    
    Returns:
        Flask Response对象
    """
    response_data = {
        "code": code,
        "msg": msg,
        "serial_number": str(int(time.time() * 1000)),  # 时间戳（毫秒）
        "data": data if data is not None else {}
    }
    return jsonify(response_data)

def error_response(msg="Error", code=0, data=None):
    """
    错误响应格式
    
    Args:
        msg: 错误消息
        code: 错误码，0表示失败
        data: 额外数据
    
    Returns:
        Flask Response对象
    """
    response_data = {
        "code": code,
        "msg": msg,
        "serial_number": str(int(time.time() * 1000)),  # 时间戳（毫秒）
        "data": data if data is not None else {}
    }
    return jsonify(response_data)

def paginated_response(data, pagination, msg="Success", code=1):
    """
    分页响应格式
    
    Args:
        data: 数据列表
        pagination: 分页信息
        msg: 响应消息
        code: 响应码
    
    Returns:
        Flask Response对象
    """
    response_data = {
        "code": code,
        "msg": msg,
        "serial_number": str(int(time.time() * 1000)),
        "data": {
            "list": data,
            "pagination": pagination
        }
    }
    return jsonify(response_data)
