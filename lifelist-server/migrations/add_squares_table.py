"""
数据库迁移脚本：添加squares表和相关字段
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db

def upgrade():
    """添加squares表和相关字段"""
    app = create_app()
    with app.app_context():
        # 创建squares表
        try:
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    CREATE TABLE IF NOT EXISTS squares (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        original_list_id INTEGER NOT NULL,
                        author_id INTEGER NOT NULL,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        cover_image VARCHAR(255),
                        items TEXT,
                        follow_count INTEGER DEFAULT 0 NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                        FOREIGN KEY (original_list_id) REFERENCES lists(id),
                        FOREIGN KEY (author_id) REFERENCES users(id)
                    )
                """))
                conn.commit()
            print("创建squares表成功")
        except Exception as e:
            print(f"创建squares表失败或已存在: {e}")
        
        # 添加square_id字段到lists表
        try:
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    ALTER TABLE lists 
                    ADD COLUMN square_id INTEGER
                """))
                conn.commit()
            print("添加square_id字段成功")
        except Exception as e:
            print(f"添加square_id字段失败或已存在: {e}")
        
        # 添加外键约束
        try:
            with db.engine.connect() as conn:
                conn.execute(db.text("""
                    CREATE INDEX IF NOT EXISTS idx_lists_square_id ON lists(square_id)
                """))
                conn.commit()
            print("添加索引成功")
        except Exception as e:
            print(f"添加索引失败: {e}")
        
        print("数据库迁移完成")

def downgrade():
    """回滚迁移"""
    app = create_app()
    with app.app_context():
        try:
            with db.engine.connect() as conn:
                # 删除索引
                conn.execute(db.text("DROP INDEX IF EXISTS idx_lists_square_id"))
                
                # 删除square_id字段
                conn.execute(db.text("""
                    ALTER TABLE lists 
                    DROP COLUMN IF EXISTS square_id
                """))
                
                # 删除squares表
                conn.execute(db.text("DROP TABLE IF EXISTS squares"))
                
                conn.commit()
            print("数据库迁移回滚完成")
        except Exception as e:
            print(f"回滚失败: {e}")

if __name__ == '__main__':
    upgrade()
