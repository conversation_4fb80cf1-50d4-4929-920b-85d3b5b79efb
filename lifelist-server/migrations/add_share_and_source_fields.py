"""
数据库迁移脚本：添加分享到广场和来源字段
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db

def upgrade():
    """添加新字段"""
    app = create_app()
    with app.app_context():
        # 添加 is_shared_to_square 字段
        db.engine.execute("""
            ALTER TABLE lists 
            ADD COLUMN is_shared_to_square BOOLEAN DEFAULT FALSE NOT NULL
        """)
        
        # 添加 original_list_id 字段
        db.engine.execute("""
            ALTER TABLE lists 
            ADD COLUMN original_list_id INTEGER
        """)
        
        # 添加外键约束
        db.engine.execute("""
            ALTER TABLE lists 
            ADD CONSTRAINT fk_lists_original_list_id 
            FOREIGN KEY (original_list_id) REFERENCES lists(id)
        """)
        
        print("数据库迁移完成：添加了 is_shared_to_square 和 original_list_id 字段")

def downgrade():
    """回滚迁移"""
    app = create_app()
    with app.app_context():
        # 删除外键约束
        db.engine.execute("""
            ALTER TABLE lists 
            DROP CONSTRAINT IF EXISTS fk_lists_original_list_id
        """)
        
        # 删除字段
        db.engine.execute("""
            ALTER TABLE lists 
            DROP COLUMN IF EXISTS original_list_id
        """)
        
        db.engine.execute("""
            ALTER TABLE lists 
            DROP COLUMN IF EXISTS is_shared_to_square
        """)
        
        print("数据库迁移回滚完成")

if __name__ == '__main__':
    upgrade()
