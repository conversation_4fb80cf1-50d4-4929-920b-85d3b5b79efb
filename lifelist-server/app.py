"""
Flask application factory
"""
from flask import Flask, render_template
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON>TManager
from config import Config

# 全局扩展对象
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app(config_class=Config):
    """创建Flask应用实例"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)
    
    # 注册蓝图
    from routes.auth import auth_bp
    from routes.lists import lists_bp
    from routes.squares import squares_bp
    from routes.items import items_bp
    from routes.users import users_bp
    from routes.admin import admin_bp
    from routes.common import common_bp

    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
    app.register_blueprint(squares_bp, url_prefix='/api/v1/squares')
    app.register_blueprint(lists_bp, url_prefix='/api/v1/lists')
    app.register_blueprint(items_bp, url_prefix='/api/v1/items')
    app.register_blueprint(users_bp, url_prefix='/api/v1/user')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(common_bp, url_prefix='/api/v1/common')

    # 静态文件服务 - 用于提供上传的文件
    @app.route('/static/uploads/<path:filename>')
    def uploaded_file(filename):
        """提供上传文件的访问"""
        from flask import send_from_directory
        upload_folder = app.config['UPLOAD_FOLDER']
        return send_from_directory(upload_folder, filename)

    @app.route("/privacy")
    def privacy_policy():
        return render_template('privacy.html', app_name='美瞬人生清单')

    @app.route("/useragreement")
    def user_agreement():
        return render_template('useragreement.html', app_name='美瞬人生清单')

    # 健康检查
    @app.route("/health")
    def health_check():
        return {"status": "ok", "message": "LifeList API is running"}
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        # 初始化示例数据
        init_sample_data()
    
    return app

def init_sample_data():
    """初始化示例数据"""
    from models.user import User
    from models.list import List
    from models.item import Item
    from datetime import datetime
    import uuid
    
    # 检查是否已有数据
    if User.query.first():
        return
    
    # 创建示例用户
    sample_user = User(
        uuid=str(uuid.uuid4()),
        nickname='示例用户',
        avatar='https://example.com/avatar.jpg',
        is_vip=False
    )
    db.session.add(sample_user)
    db.session.commit()
    
    # 创建示例清单
    sample_lists = [
        {
            'name': '我去过的中国城市',
            'description': '记录我去过的所有中国城市',
            'cover_image': 'https://example.com/cities.jpg',
            'items': ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '西安', '重庆', '天津']
        },
        {
            'name': '豆瓣Top250电影',
            'description': '看完豆瓣评分最高的250部电影',
            'cover_image': 'https://example.com/movies.jpg',
            'items': ['肖申克的救赎', '霸王别姬', '阿甘正传', '这个杀手不太冷', '美丽人生', '泰坦尼克号', '千与千寻', '辛德勒的名单', '盗梦空间', '忠犬八公的故事']
        },
        {
            'name': '和另一半一起完成的100件事',
            'description': '情侣必做的浪漫清单',
            'cover_image': 'https://example.com/couple.jpg',
            'items': ['一起看日出', '一起做饭', '一起旅行', '一起看电影', '一起运动', '互相拍照', '一起学习新技能', '一起养宠物', '一起种植物', '一起写日记']
        }
    ]
    
    for list_data in sample_lists:
        new_list = List(
            user_id=sample_user.id,
            name=list_data['name'],
            description=list_data['description'],
            cover_image=list_data['cover_image']
        )
        db.session.add(new_list)
        db.session.commit()
        
        # 添加子项
        for i, item_name in enumerate(list_data['items']):
            item = Item(
                list_id=new_list.id,
                name=item_name,
                is_completed=i < 3  # 前3个标记为已完成
            )
            db.session.add(item)
    
    db.session.commit()
    print("Sample data initialized successfully!")
