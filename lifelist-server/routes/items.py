"""
Items routes
"""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from models.list import List
from models.item import Item
from app import db
from utils.response import success_response, error_response

items_bp = Blueprint('items', __name__)

@items_bp.route('', methods=['POST'])
@jwt_required()
def create_item():
    """添加子项"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        list_id = data.get('list_id')
        name = data.get('name', '').strip()
        
        if not list_id or not name:
            return error_response(msg='清单ID和子项名称不能为空'), 400

        # 检查清单是否存在
        list_item = List.query.get(list_id)
        if not list_item:
            return error_response(msg='清单不存在'), 404

        # 检查权限（只有清单创建者可以添加子项）
        if f'{list_item.user_id}' != current_user_id:
            return error_response(msg=f'无权限向此清单添加子项 {list_item.user_id} != {current_user_id}'), 403
        
        # 创建新子项
        new_item = Item(
            list_id=list_id,
            name=name,
            is_completed=False
        )
        
        db.session.add(new_item)
        db.session.commit()
        
        return success_response(data=new_item.to_dict())

    except Exception as e:
        db.session.rollback()
        return error_response(msg=f'添加子项失败: {str(e)}'), 500

@items_bp.route('/<int:item_id>', methods=['PUT'])
@jwt_required()
def update_item(item_id):
    """更新子项"""
    try:
        current_user_id = get_jwt_identity()
        item = Item.query.get(item_id)
        
        if not item:
            return error_response(msg='子项不存在'), 404
        
        # 检查权限
        if f'{item.list.user_id}' != current_user_id:
            return error_response(msg='无权限修改此子项'), 403
        
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return error_response(msg='子项名称不能为空'), 400
            item.name = name
        
        if 'is_completed' in data:
            item.is_completed = bool(data['is_completed'])
        
        if 'sort_order' in data:
            item.sort_order = int(data['sort_order'])
        
        db.session.commit()
        
        return success_response(data=item.to_dict()
        )
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新子项失败: {str(e)}'
        }), 500

@items_bp.route('/<int:item_id>', methods=['DELETE'])
@jwt_required()
def delete_item(item_id):
    """删除子项"""
    try:
        current_user_id = get_jwt_identity()
        item = Item.query.get(item_id)
        
        if not item:
            return error_response(msg='子项不存在'), 404
        
        # 检查权限
        if f'{item.list.user_id}' != current_user_id:
            return error_response(msg='无权限删除此子项'), 403
        
        db.session.delete(item)
        db.session.commit()
        
        return success_response(msg='子项删除成功')
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除子项失败: {str(e)}'
        }), 500

@items_bp.route('/<int:item_id>/toggle', methods=['POST'])
@jwt_required()
def toggle_item_completion(item_id):
    """切换子项完成状态"""
    try:
        current_user_id = get_jwt_identity()
        item = Item.query.get(item_id)
        
        if not item:
            return error_response(msg='子项不存在'), 404
        
        # 检查权限
        if f'{item.list.user_id}' != current_user_id:
            return error_response(msg='无权限修改此子项'), 403
        
        # 切换完成状态
        new_status = item.toggle_completion()
        db.session.commit()
        
        return success_response(data={
                'id': item.id,
                'is_completed': new_status
        }, msg='子项更新成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response({
            'success': False,
            'message': f'切换完成状态失败: {str(e)}'
        }), 500

@items_bp.route('/batch', methods=['PUT'])
@jwt_required()
def batch_update_items():
    """批量更新子项（用于排序等）"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        items_data = data.get('items', [])
        
        if not items_data:
            return error_response(msg='没有要更新的子项'), 400
        
        updated_items = []
        
        for item_data in items_data:
            item_id = item_data.get('id')
            if not item_id:
                continue
            
            item = Item.query.get(item_id)
            if not item:
                continue
            
            # 检查权限
            if f'{item.list.user_id}' != current_user_id:
                continue
            
            # 更新排序
            if 'sort_order' in item_data:
                item.sort_order = int(item_data['sort_order'])
                updated_items.append(item.to_dict())
        
        db.session.commit()
        
        return success_response({
            'success': True,
            'data': updated_items,
            'message': f'成功更新{len(updated_items)}个子项'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'批量更新失败: {str(e)}'
        }), 500
