"""
Users routes
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from app import db
from utils.response import success_response, error_response
from datetime import datetime

users_bp = Blueprint('users', __name__)

@users_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    """获取用户信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        return success_response(data=user.to_dict()
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户信息失败: {str(e)}'
        }), 500

@users_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_user_profile():
    """更新用户信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        data = request.get_json()
        
        # 更新字段
        if 'nickname' in data:
            nickname = data['nickname'].strip()
            if not nickname:
                return error_response(msg='昵称不能为空'), 400
            user.nickname = nickname
        
        if 'avatar' in data:
            user.avatar = data['avatar'].strip()
        
        if 'birthdate' in data:
            birthdate_str = data['birthdate']
            if birthdate_str:
                try:
                    user.birthdate = datetime.strptime(birthdate_str, '%Y-%m-%d').date()
                except ValueError:
                    return error_response(msg='出生日期格式错误，请使用YYYY-MM-DD格式'), 400
            else:
                user.birthdate = None
        
        db.session.commit()
        
        return success_response(data=user.to_dict()
        )
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新用户信息失败: {str(e)}'
        }), 500

@users_bp.route('/vip', methods=['GET'])
@jwt_required()
def get_user_vip_status():
    """查询用户VIP状态"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user.id,
                'is_vip': user.is_vip,
                'list_count': user.get_list_count(),
                'can_create_list': user.can_create_list()
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取VIP状态失败: {str(e)}'
        }), 500

@users_bp.route('/vip/upgrade', methods=['POST'])
@jwt_required()
def upgrade_to_vip():
    """升级为VIP用户"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        if user.is_vip:
            return error_response(msg='用户已经是VIP'), 400
        
        # 这里应该集成支付系统
        # 暂时直接升级为VIP
        user.is_vip = True
        db.session.commit()
        
        return success_response(msg='VIP升级成功', data=user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'VIP升级失败: {str(e)}'
        }), 500

@users_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_user_stats():
    """获取用户统计信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        # 统计用户数据
        from models.list import List
        from models.item import Item
        from models.follow import Follow
        
        created_lists_count = List.query.filter_by(user_id=current_user_id).count()
        followed_lists_count = Follow.query.filter_by(user_id=current_user_id).count()
        
        # 统计完成的子项数量
        completed_items_count = db.session.query(Item).join(List).filter(
            List.user_id == current_user_id,
            Item.is_completed == True
        ).count()
        
        total_items_count = db.session.query(Item).join(List).filter(
            List.user_id == current_user_id
        ).count()
        
        return jsonify({
            'success': True,
            'data': {
                'created_lists_count': created_lists_count,
                'followed_lists_count': followed_lists_count,
                'total_lists_count': created_lists_count + followed_lists_count,
                'completed_items_count': completed_items_count,
                'total_items_count': total_items_count,
                'completion_rate': completed_items_count / total_items_count if total_items_count > 0 else 0
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户统计失败: {str(e)}'
        }), 500

@users_bp.route('/delete-account', methods=['DELETE'])
@jwt_required()
def delete_account():
    """注销账户"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user:
            return error_response(msg='用户不存在'), 404

        # 删除用户相关的所有数据
        from models.list import List
        from models.item import Item
        from models.follow import Follow

        # 1. 删除用户的清单项
        user_lists = List.query.filter_by(user_id=current_user_id).all()
        for user_list in user_lists:
            Item.query.filter_by(list_id=user_list.id).delete()

        # 2. 删除用户的关注记录
        Follow.query.filter_by(user_id=current_user_id).delete()

        # 3. 删除用户的清单
        List.query.filter_by(user_id=current_user_id).delete()

        # 4. 删除用户账户
        db.session.delete(user)

        db.session.commit()

        return success_response(msg='账户注销成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete account error: {str(e)}")
        return error_response(msg='账户注销失败'), 500
