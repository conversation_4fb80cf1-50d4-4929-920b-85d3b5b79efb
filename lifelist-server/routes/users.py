"""
Users routes
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from app import db
from utils.response import success_response, error_response
from datetime import datetime, date
import random

users_bp = Blueprint('users', __name__)

@users_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    """获取用户信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        return success_response(data=user.to_dict()
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户信息失败: {str(e)}'
        }), 500

@users_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_user_profile():
    """更新用户信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        data = request.get_json()
        
        # 更新字段
        if 'nickname' in data:
            nickname = data['nickname'].strip()
            if not nickname:
                return error_response(msg='昵称不能为空'), 400
            user.nickname = nickname
        
        if 'avatar' in data:
            user.avatar = data['avatar'].strip()
        
        if 'birthdate' in data:
            birthdate_str = data['birthdate']
            if birthdate_str:
                try:
                    user.birthdate = datetime.strptime(birthdate_str, '%Y-%m-%d').date()
                except ValueError:
                    return error_response(msg='出生日期格式错误，请使用YYYY-MM-DD格式'), 400
            else:
                user.birthdate = None
        
        db.session.commit()
        
        return success_response(data=user.to_dict()
        )
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新用户信息失败: {str(e)}'
        }), 500

@users_bp.route('/vip', methods=['GET'])
@jwt_required()
def get_user_vip_status():
    """查询用户VIP状态"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        return jsonify({
            'success': True,
            'data': {
                'user_id': user.id,
                'is_vip': user.is_vip,
                'list_count': user.get_list_count(),
                'can_create_list': user.can_create_list()
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取VIP状态失败: {str(e)}'
        }), 500

@users_bp.route('/vip/upgrade', methods=['POST'])
@jwt_required()
def upgrade_to_vip():
    """升级为VIP用户"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        if user.is_vip:
            return error_response(msg='用户已经是VIP'), 400
        
        # 这里应该集成支付系统
        # 暂时直接升级为VIP
        user.is_vip = True
        db.session.commit()
        
        return success_response(msg='VIP升级成功', data=user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'VIP升级失败: {str(e)}'
        }), 500

@users_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_user_stats():
    """获取用户统计信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404
        
        # 统计用户数据
        from models.list import List
        from models.item import Item
        from models.follow import Follow
        
        created_lists_count = List.query.filter_by(user_id=current_user_id).count()
        followed_lists_count = Follow.query.filter_by(user_id=current_user_id).count()
        
        # 统计完成的子项数量
        completed_items_count = db.session.query(Item).join(List).filter(
            List.user_id == current_user_id,
            Item.is_completed == True
        ).count()
        
        total_items_count = db.session.query(Item).join(List).filter(
            List.user_id == current_user_id
        ).count()
        
        return jsonify({
            'success': True,
            'data': {
                'created_lists_count': created_lists_count,
                'followed_lists_count': followed_lists_count,
                'total_lists_count': created_lists_count + followed_lists_count,
                'completed_items_count': completed_items_count,
                'total_items_count': total_items_count,
                'completion_rate': completed_items_count / total_items_count if total_items_count > 0 else 0
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户统计失败: {str(e)}'
        }), 500

@users_bp.route('/delete-account', methods=['DELETE'])
@jwt_required()
def delete_account():
    """注销账户"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user:
            return error_response(msg='用户不存在'), 404

        # 删除用户相关的所有数据
        from models.list import List
        from models.item import Item
        from models.follow import Follow

        # 1. 删除用户的清单项
        user_lists = List.query.filter_by(user_id=current_user_id).all()
        for user_list in user_lists:
            Item.query.filter_by(list_id=user_list.id).delete()

        # 2. 删除用户的关注记录
        Follow.query.filter_by(user_id=current_user_id).delete()

        # 3. 删除用户的清单
        List.query.filter_by(user_id=current_user_id).delete()

        # 4. 删除用户账户
        db.session.delete(user)

        db.session.commit()

        return success_response(msg='账户注销成功')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete account error: {str(e)}")
        return error_response(msg='账户注销失败'), 500


@users_bp.route('/remaining', methods=['GET'])
@jwt_required()
def get_remaining_life():
    """获取余生时钟信息"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user:
            return error_response(msg='用户不存在'), 404

        if not user.birthdate:
            return error_response(msg='请先设置生日信息'), 400

        # 计算年龄信息
        age_info = _calculate_age_info(user.birthdate)

        # 生成生活事件列表
        all_events = _generate_life_events(age_info)

        # 随机选择4个事件用于显示
        display_events = random.sample(all_events, min(4, len(all_events)))

        return success_response(data={
            'age_info': age_info,
            'life_events': display_events,
            'all_events': all_events
        })

    except Exception as e:
        current_app.logger.error(f"Get remaining life error: {str(e)}")
        return error_response(msg=f'获取余生信息失败: {str(e)}'), 500


def _calculate_age_info(birthdate):
    """计算年龄相关信息"""
    today = date.today()

    # 计算已生活的天数
    total_days_lived = (today - birthdate).days

    # 计算精确年龄
    years = today.year - birthdate.year
    if today.month < birthdate.month or (today.month == birthdate.month and today.day < birthdate.day):
        years -= 1

    # 计算今年已过的天数
    this_year_start = date(today.year, 1, 1)
    days_this_year = (today - this_year_start).days

    # 计算今年总天数
    next_year_start = date(today.year + 1, 1, 1)
    total_days_this_year = (next_year_start - this_year_start).days

    # 计算小数部分的年龄
    year_fraction = days_this_year / total_days_this_year
    precise_age = years + year_fraction

    # 计算距离下一个生日的天数
    next_birthday = date(today.year, birthdate.month, birthdate.day)
    if next_birthday < today:
        next_birthday = date(today.year + 1, birthdate.month, birthdate.day)

    days_to_birthday = (next_birthday - today).days

    # 计算当前时间的小时、分钟、秒（模拟）
    import time
    current_time = time.localtime()
    hours = current_time.tm_hour
    minutes = current_time.tm_min
    seconds = current_time.tm_sec

    return {
        'years': years,
        'days': days_to_birthday,
        'hours': hours,
        'minutes': minutes,
        'seconds': seconds,
        'total_days_lived': total_days_lived,
        'formatted_age': f"{precise_age:.8f}",
        'precise_age': precise_age
    }


def _generate_life_events(age_info):
    """生成生活事件列表"""
    current_age = age_info['precise_age']

    # 假设平均寿命80岁
    life_expectancy = 80
    remaining_years = max(0, life_expectancy - current_age)

    events = []

    # 世界杯（4年一次）
    world_cups = int(remaining_years / 4)
    if world_cups > 0:
        events.append({
            'id': 'world_cup',
            'icon': '⚽',
            'title': f'观看 {world_cups} 届世界杯',
            'description': '4年一次的足球盛宴'
        })

    # 奥运会（4年一次，与世界杯错开2年）
    olympics = int((remaining_years + 2) / 4)
    if olympics > 0:
        events.append({
            'id': 'olympics',
            'icon': '🏅',
            'title': f'观看 {olympics} 届奥运会',
            'description': '4年一次的体育盛会'
        })

    # 春节（1年一次）
    spring_festivals = int(remaining_years)
    if spring_festivals > 0:
        events.append({
            'id': 'spring_festival',
            'icon': '🧧',
            'title': f'度过 {spring_festivals} 个春节',
            'description': '与家人团聚的美好时光'
        })

    # 圣诞节（1年一次）
    christmas = int(remaining_years)
    if christmas > 0:
        events.append({
            'id': 'christmas',
            'icon': '🎄',
            'title': f'享受 {christmas} 个圣诞',
            'description': '温馨浪漫的节日氛围'
        })

    # 堆雪人（1年1.5次）
    snowmen = int(remaining_years * 1.5)
    if snowmen > 0:
        events.append({
            'id': 'snowman',
            'icon': '⛄',
            'title': f'堆 {snowmen} 次雪人',
            'description': '冬日里的童真乐趣'
        })

    # 旅行度假（1年3次）
    travels = int(remaining_years * 3)
    if travels > 0:
        events.append({
            'id': 'travel',
            'icon': '✈️',
            'title': f'去旅行 {travels} 次',
            'description': '探索世界的美好'
        })

    # 美丽晚霞（1年5次）
    sunsets = int(remaining_years * 5)
    if sunsets > 0:
        events.append({
            'id': 'sunset',
            'icon': '🌅',
            'title': f'欣赏 {sunsets} 次美丽晚霞',
            'description': '大自然的绚烂画卷'
        })

    # 退休（假设65岁退休）
    retirement_age = 65
    years_to_retirement = max(0, retirement_age - current_age)
    if years_to_retirement > 0:
        events.append({
            'id': 'retirement',
            'icon': '🏖️',
            'title': f'还有 {int(years_to_retirement)} 年退休',
            'description': '享受悠闲的退休生活'
        })

    # 周末（每7天2次）
    weekends = int(remaining_years * 52 * 2)
    if weekends > 0:
        events.append({
            'id': 'weekend',
            'icon': '🎉',
            'title': f'度过 {weekends} 个周末',
            'description': '每周两天的休闲时光'
        })

    # 夏天
    summers = int(remaining_years)
    if summers > 0:
        events.append({
            'id': 'summer',
            'icon': '☀️',
            'title': f'享受 {summers} 个夏天',
            'description': '感受夏日的美好时光'
        })

    # 生日
    birthdays = int(remaining_years)
    if birthdays > 0:
        events.append({
            'id': 'birthday',
            'icon': '🎂',
            'title': f'庆祝 {birthdays} 个生日',
            'description': '每年一次的特殊日子'
        })

    # 重大疾病提醒（概率性事件）
    events.append({
        'id': 'health_warning',
        'icon': '⚠️',
        'title': '可能只有1次重大疾病的机会',
        'description': '珍惜健康，关爱身体'
    })

    return events
