"""
Admin routes
"""
from flask import Blueprint, request, jsonify, render_template, session, redirect, url_for
from models.user import User
from models.list import List
from models.item import Item
from models.follow import Follow
from app import db
from config import Config
import csv
import io
from datetime import datetime

admin_bp = Blueprint('admin', __name__)

def require_admin_login():
    """检查管理员登录状态"""
    return session.get('admin_logged_in', False)

@admin_bp.route('/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录"""
    if request.method == 'GET':
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>LifeList Admin Login</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 50px; }
                .login-form { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 3px; }
                button { width: 100%; padding: 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
                button:hover { background: #005a87; }
                .error { color: red; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h2>LifeList Admin Login</h2>
                <form method="post">
                    <input type="text" name="username" placeholder="Username" required>
                    <input type="password" name="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
            </div>
        </body>
        </html>
        '''
    
    username = request.form.get('username')
    password = request.form.get('password')
    
    if username == Config.ADMIN_USERNAME and password == Config.ADMIN_PASSWORD:
        session['admin_logged_in'] = True
        return redirect(url_for('admin.admin_dashboard'))
    else:
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>LifeList Admin Login</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 50px; }
                .login-form { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 3px; }
                button { width: 100%; padding: 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
                button:hover { background: #005a87; }
                .error { color: red; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h2>LifeList Admin Login</h2>
                <div class="error">Invalid username or password</div>
                <form method="post">
                    <input type="text" name="username" placeholder="Username" required>
                    <input type="password" name="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
            </div>
        </body>
        </html>
        '''

@admin_bp.route('/logout')
def admin_logout():
    """管理员登出"""
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin.admin_login'))

@admin_bp.route('/')
@admin_bp.route('/dashboard')
def admin_dashboard():
    """管理员仪表板"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))
    
    # 统计数据
    total_users = User.query.count()
    total_lists = List.query.count()
    total_items = Item.query.count()
    total_follows = Follow.query.count()
    vip_users = User.query.filter_by(is_vip=True).count()
    
    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>LifeList Admin Dashboard</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background: #007cba; color: white; padding: 20px; margin: -20px -20px 20px -20px; }}
            .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
            .stat-card {{ background: #f5f5f5; padding: 20px; border-radius: 5px; flex: 1; text-align: center; }}
            .stat-number {{ font-size: 2em; font-weight: bold; color: #007cba; }}
            .nav {{ margin: 20px 0; }}
            .nav a {{ display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }}
            .nav a:hover {{ background: #005a87; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>LifeList Admin Dashboard</h1>
            <a href="/admin/logout" style="color: white; float: right;">Logout</a>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_users}</div>
                <div>Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{vip_users}</div>
                <div>VIP Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_lists}</div>
                <div>Total Lists</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_items}</div>
                <div>Total Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_follows}</div>
                <div>Total Follows</div>
            </div>
        </div>
        
        <div class="nav">
            <a href="/admin/users">Manage Users</a>
            <a href="/admin/lists">Manage Lists</a>
            <a href="/admin/items">Manage Items</a>
            <a href="/admin/export/users">Export Users CSV</a>
            <a href="/admin/export/lists">Export Lists CSV</a>
            <a href="/admin/export/items">Export Items CSV</a>
        </div>
    </body>
    </html>
    '''

@admin_bp.route('/users')
def admin_users():
    """用户管理页面"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))
    
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=20, error_out=False)
    
    users_html = ""
    for user in users.items:
        users_html += f'''
        <tr>
            <td>{user.id}</td>
            <td>{user.nickname}</td>
            <td>{user.uuid}</td>
            <td>{"Yes" if user.is_vip else "No"}</td>
            <td>{user.get_list_count()}</td>
            <td>{user.created_at.strftime("%Y-%m-%d %H:%M")}</td>
            <td>
                <a href="/admin/users/{user.id}/toggle-vip">Toggle VIP</a>
            </td>
        </tr>
        '''
    
    pagination_html = ""
    if users.has_prev:
        pagination_html += f'<a href="/admin/users?page={users.prev_num}">Previous</a> '
    pagination_html += f'Page {users.page} of {users.pages} '
    if users.has_next:
        pagination_html += f'<a href="/admin/users?page={users.next_num}">Next</a>'
    
    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>User Management - LifeList Admin</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .header {{ background: #007cba; color: white; padding: 20px; margin: -20px -20px 20px -20px; }}
            .pagination {{ margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>User Management</h1>
            <a href="/admin/dashboard" style="color: white;">Back to Dashboard</a>
        </div>
        
        <table>
            <tr>
                <th>ID</th>
                <th>Nickname</th>
                <th>UUID</th>
                <th>VIP</th>
                <th>Lists Count</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
            {users_html}
        </table>
        
        <div class="pagination">
            {pagination_html}
        </div>
    </body>
    </html>
    '''

@admin_bp.route('/users/<int:user_id>/toggle-vip')
def toggle_user_vip(user_id):
    """切换用户VIP状态"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))
    
    user = User.query.get_or_404(user_id)
    user.is_vip = not user.is_vip
    db.session.commit()
    
    return redirect(url_for('admin.admin_users'))

@admin_bp.route('/export/users')
def export_users_csv():
    """导出用户CSV"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入标题行
    writer.writerow(['ID', 'UUID', 'Nickname', 'Avatar', 'Birthdate', 'Is VIP', 'Lists Count', 'Created At', 'Updated At'])
    
    # 写入数据
    users = User.query.all()
    for user in users:
        writer.writerow([
            user.id,
            user.uuid,
            user.nickname,
            user.avatar or '',
            user.birthdate.isoformat() if user.birthdate else '',
            user.is_vip,
            user.get_list_count(),
            user.created_at.isoformat(),
            user.updated_at.isoformat()
        ])
    
    output.seek(0)
    
    from flask import Response
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename=users_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
    )

@admin_bp.route('/lists')
def admin_lists():
    """清单管理页面"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))

    page = request.args.get('page', 1, type=int)
    lists = List.query.paginate(page=page, per_page=20, error_out=False)

    lists_html = ""
    for list_item in lists.items:
        lists_html += f'''
        <tr>
            <td>{list_item.id}</td>
            <td>{list_item.name}</td>
            <td>{list_item.creator.nickname}</td>
            <td>{"Yes" if list_item.is_public else "No"}</td>
            <td>{list_item.get_total_count()}</td>
            <td>{list_item.get_completed_count()}</td>
            <td>{list_item.get_follow_count()}</td>
            <td>{list_item.created_at.strftime("%Y-%m-%d %H:%M")}</td>
            <td>
                <a href="/admin/lists/{list_item.id}/toggle-public">Toggle Public</a> |
                <a href="/admin/lists/{list_item.id}/delete" onclick="return confirm('Are you sure?')">Delete</a>
            </td>
        </tr>
        '''

    pagination_html = ""
    if lists.has_prev:
        pagination_html += f'<a href="/admin/lists?page={lists.prev_num}">Previous</a> '
    pagination_html += f'Page {lists.page} of {lists.pages} '
    if lists.has_next:
        pagination_html += f'<a href="/admin/lists?page={lists.next_num}">Next</a>'

    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>List Management - LifeList Admin</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .header {{ background: #007cba; color: white; padding: 20px; margin: -20px -20px 20px -20px; }}
            .pagination {{ margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>List Management</h1>
            <a href="/admin/dashboard" style="color: white;">Back to Dashboard</a>
        </div>

        <table>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Creator</th>
                <th>Public</th>
                <th>Total Items</th>
                <th>Completed Items</th>
                <th>Follows</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
            {lists_html}
        </table>

        <div class="pagination">
            {pagination_html}
        </div>
    </body>
    </html>
    '''

@admin_bp.route('/lists/<int:list_id>/toggle-public')
def toggle_list_public(list_id):
    """切换清单公开状态"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))

    list_item = List.query.get_or_404(list_id)
    list_item.is_public = not list_item.is_public
    db.session.commit()

    return redirect(url_for('admin.admin_lists'))

@admin_bp.route('/lists/<int:list_id>/delete')
def delete_list_admin(list_id):
    """删除清单"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))

    list_item = List.query.get_or_404(list_id)
    db.session.delete(list_item)
    db.session.commit()

    return redirect(url_for('admin.admin_lists'))

@admin_bp.route('/export/lists')
def export_lists_csv():
    """导出清单CSV"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))

    output = io.StringIO()
    writer = csv.writer(output)

    # 写入标题行
    writer.writerow(['ID', 'Name', 'Description', 'Creator', 'Is Public', 'Total Items', 'Completed Items', 'Follows', 'Created At', 'Updated At'])

    # 写入数据
    lists = List.query.all()
    for list_item in lists:
        writer.writerow([
            list_item.id,
            list_item.name,
            list_item.description or '',
            list_item.creator.nickname,
            list_item.is_public,
            list_item.get_total_count(),
            list_item.get_completed_count(),
            list_item.get_follow_count(),
            list_item.created_at.isoformat(),
            list_item.updated_at.isoformat()
        ])

    output.seek(0)

    from flask import Response
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename=lists_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
    )

@admin_bp.route('/export/items')
def export_items_csv():
    """导出子项CSV"""
    if not require_admin_login():
        return redirect(url_for('admin.admin_login'))

    output = io.StringIO()
    writer = csv.writer(output)

    # 写入标题行
    writer.writerow(['ID', 'Name', 'List Name', 'List Creator', 'Is Completed', 'Sort Order', 'Created At', 'Updated At'])

    # 写入数据
    items = Item.query.join(List).all()
    for item in items:
        writer.writerow([
            item.id,
            item.name,
            item.list.name,
            item.list.creator.nickname,
            item.is_completed,
            item.sort_order,
            item.created_at.isoformat(),
            item.updated_at.isoformat()
        ])

    output.seek(0)

    from flask import Response
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename=items_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
    )
