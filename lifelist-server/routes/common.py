"""
Common routes for file upload and other shared functionality
"""
import os
import uuid
from datetime import datetime
from flask import Blueprint, request, current_app, url_for
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
from flask_jwt_extended import jwt_required, get_jwt_identity
from utils.response import success_response, error_response

common_bp = Blueprint('common', __name__)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def ensure_upload_folder():
    """确保上传文件夹存在"""
    upload_folder = current_app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@common_bp.route('/upload', methods=['POST'])
@jwt_required()
def upload_file():
    """
    文件上传接口
    支持图片文件上传，返回文件URL
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return error_response(msg='没有选择文件'), 400
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return error_response(msg='没有选择文件'), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            allowed_types = ', '.join(current_app.config['ALLOWED_EXTENSIONS'])
            return error_response(msg=f'不支持的文件类型:{file.filename}，支持的类型：{allowed_types}'), 400
        
        # 确保上传文件夹存在
        upload_folder = ensure_upload_folder()
        
        # 生成唯一文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        
        # 按日期创建子文件夹
        date_folder = datetime.now().strftime('%Y/%m/%d')
        full_upload_path = os.path.join(upload_folder, date_folder)
        
        if not os.path.exists(full_upload_path):
            os.makedirs(full_upload_path)
        
        # 保存文件
        file_path = os.path.join(full_upload_path, unique_filename)
        file.save(file_path)
        
        # 生成文件URL
        # 这里假设有一个静态文件服务，实际部署时需要配置nginx或其他静态文件服务
        file_url = f"/static/uploads/{date_folder}/{unique_filename}"
        
        # 如果有域名配置，可以返回完整URL
        if hasattr(current_app.config, 'DOMAIN'):
            file_url = f"{current_app.config['DOMAIN']}{file_url}"
        
        return success_response(data={
            'url': file_url,
            'filename': unique_filename,
            'original_filename': file.filename,
            'size': os.path.getsize(file_path),
            'upload_time': datetime.now().isoformat()
        }, msg='文件上传成功')
        
    except RequestEntityTooLarge:
        max_size = current_app.config['MAX_CONTENT_LENGTH'] // (1024 * 1024)
        return error_response(msg=f'文件过大，最大支持{max_size}MB'), 413
    
    except Exception as e:
        current_app.logger.error(f"File upload error: {str(e)}")
        return error_response(msg='文件上传失败'), 500

@common_bp.route('/upload/avatar', methods=['POST'])
@jwt_required()
def upload_avatar():
    """
    头像上传接口
    专门用于用户头像上传，会进行额外的处理
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 检查是否有文件
        if 'file' not in request.files:
            return error_response(msg='没有选择头像文件'), 400
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return error_response(msg='没有选择头像文件'), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return error_response(msg='头像只支持图片格式（png, jpg, jpeg, gif）'), 400
        
        # 确保上传文件夹存在
        upload_folder = ensure_upload_folder()
        
        # 生成唯一文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"avatar_{current_user_id}_{uuid.uuid4().hex}.{file_extension}"
        
        # 头像放在专门的文件夹
        avatar_folder = os.path.join(upload_folder, 'avatars')
        if not os.path.exists(avatar_folder):
            os.makedirs(avatar_folder)
        
        # 保存文件
        file_path = os.path.join(avatar_folder, unique_filename)
        file.save(file_path)
        
        # 生成文件URL
        file_url = f"/static/uploads/avatars/{unique_filename}"
        
        if hasattr(current_app.config, 'DOMAIN'):
            file_url = f"{current_app.config['DOMAIN']}{file_url}"
        
        return success_response(data={
            'url': file_url,
            'filename': unique_filename,
            'original_filename': file.filename,
            'size': os.path.getsize(file_path),
            'upload_time': datetime.now().isoformat()
        }, msg='头像上传成功')
        
    except RequestEntityTooLarge:
        max_size = current_app.config['MAX_CONTENT_LENGTH'] // (1024 * 1024)
        return error_response(msg=f'头像文件过大，最大支持{max_size}MB'), 413
    
    except Exception as e:
        current_app.logger.error(f"Avatar upload error: {str(e)}")
        return error_response(msg='头像上传失败'), 500

@common_bp.route('/upload/info', methods=['GET'])
@jwt_required()
def upload_info():
    """
    获取上传配置信息
    """
    try:
        max_size = current_app.config['MAX_CONTENT_LENGTH'] // (1024 * 1024)
        allowed_types = list(current_app.config['ALLOWED_EXTENSIONS'])
        
        return success_response(data={
            'max_size_mb': max_size,
            'allowed_extensions': allowed_types,
            'upload_folder': current_app.config['UPLOAD_FOLDER']
        }, msg='获取上传配置成功')
        
    except Exception as e:
        current_app.logger.error(f"Get upload info error: {str(e)}")
        return error_response(msg='获取上传配置失败'), 500
