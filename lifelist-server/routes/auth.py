"""
Authentication routes
"""
from flask import Blueprint, request
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from models.user import User
from app import db
from utils.huawei_auth import IDTokenParser
from utils.response import success_response, error_response
import uuid
import requests

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/userLogin', methods=['POST'])
def user_login():
    data = request.get_json()
    uuid = data.get('uuid')
    if not uuid:
        return error_response(msg='缺少uuid'), 400

    try:
        user = User.find_by_uuid(uuid)
        if not user:
            # 创建新用户
            user = User(
                uuid=str(uuid),
                nickname='临时用户',
            )
            db.session.add(user)
            db.session.commit()

        # 生成JWT token
        access_token = create_access_token(identity=str(user.id))

        return success_response(data={
            'user_id': user.id,
            'uuid': user.uuid,
            'nickname': user.nickname,
            'avatar': user.avatar,
            'is_vip': user.is_vip,
            'token': access_token
        })
    except Exception as e:
        return error_response(msg=f'登录失败: {str(e)}'), 500


@auth_bp.route('/huawei', methods=['POST'])
def huawei_login():
    """华为OAuth2登录"""
    data = request.get_json()
    id_token = data.get('id_token')
    openid = data.get('openid')

    if not openid:
        return error_response(msg='缺少必要参数'), 400

    if not id_token:
        return error_response(msg='缺少必要参数'), 400

    try:
        parser = IDTokenParser()
        payload = parser.verify_and_parse(id_token)

        if not payload:
            return error_response(msg='华为token验证失败'), 401
        #
        # # 获取用户信息
        # user_id = payload.get('sub')
        # email = payload.get('email')
        # name = payload.get('name')

        # 查找或创建用户
        user = User.find_by_huawei_openid(openid)
        if not user:
            # 创建新用户
            user = User(
                uuid=str(uuid.uuid4()),
                nickname=payload.get('nickname', '华为用户'),
                # avatar=user_info.get('headPictureURL'),
                huawei_openid=openid
            )
            db.session.add(user)
            db.session.commit()
        else:
            # 更新用户信息
            # user.nickname = user_info.get('displayName', user.nickname)
            # user.avatar = user_info.get('headPictureURL', user.avatar)
            # db.session.commit()
            pass

        # 生成JWT token
        access_token = create_access_token(identity=str(user.id))

        return success_response(data={
            'user_id': user.id,
            'uuid': user.uuid,
            'nickname': user.nickname,
            'avatar': user.avatar,
            'is_vip': user.is_vip,
            'token': access_token
        })

    except Exception as e:
        return error_response(msg=f'登录失败: {str(e)}'), 500


@auth_bp.route('/guest', methods=['POST'])
def guest_login():
    """游客登录"""
    data = request.get_json()
    guest_uuid = data.get('uuid')

    if not guest_uuid:
        # 生成新的游客UUID
        guest_uuid = str(uuid.uuid4())

    # 查找或创建游客用户
    user = User.find_by_uuid(guest_uuid)
    if not user:
        user = User(
            uuid=guest_uuid,
            nickname='游客用户',
            is_vip=False
        )
        db.session.add(user)
        db.session.commit()

    # 生成JWT token
    access_token = create_access_token(identity=str(user.id))

    return success_response(data={
        'user_id': user.id,
        'uuid': user.uuid,
        'nickname': user.nickname,
        'avatar': user.avatar,
        'is_vip': user.is_vip,
        'token': access_token
    })


@auth_bp.route('/refresh', methods=['POST'])
@jwt_required()
def refresh_token():
    """刷新token"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return error_response(msg='用户不存在'), 404

    new_token = create_access_token(identity=str(user.id))

    return success_response(data={
        'token': new_token
    })


def verify_huawei_token(access_token):
    """验证华为access token并获取用户信息"""
    try:
        # 调用华为API获取用户信息
        url = 'https://account.cloud.huawei.com/rest.php'
        params = {
            'nsp_svc': 'GOpen.User.getInfo',
            'nsp_ts': '',
            'access_token': access_token
        }

        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('retcode') == 0:
                return data.get('userInfo', {})

        return None
    except Exception as e:
        print(f"华为token验证失败: {e}")
        return None
