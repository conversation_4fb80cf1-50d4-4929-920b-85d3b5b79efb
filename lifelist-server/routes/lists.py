"""
Lists routes
"""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from models.user import User
from models.list import List
from models.follow import Follow
from app import db
from utils.response import success_response, error_response, paginated_response

lists_bp = Blueprint('lists', __name__)

@lists_bp.route('/recommended', methods=['GET'])
def get_recommended_lists():
    """获取推荐热门清单列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 获取推荐清单（分页）- 从squares表获取
        from models.square import SquareModel

        pagination = SquareModel.query.order_by(
            SquareModel.created_at.desc()
        ).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        lists_data = [square.to_dict() for square in pagination.items]

        return paginated_response(
            data=lists_data,
            pagination={
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        )
        
    except Exception as e:
        return error_response(msg=f'获取推荐清单失败: {str(e)}'), 500

@lists_bp.route('/my', methods=['GET'])
@jwt_required()
def get_my_lists():
    """获取用户自己的清单列表"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404

        # 获取用户的清单（创建的和关注的）
        user_lists = List.get_user_lists(current_user_id)
        lists_data = [list_item.to_dict() for list_item in user_lists]

        return success_response(data=lists_data)
        
    except Exception as e:
        return error_response(msg=f'获取我的清单失败: {str(e)}'), 500

@lists_bp.route('/addList', methods=['POST'])
@jwt_required()
def create_list():
    """创建新清单"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response(msg='用户不存在'), 404

        # 检查用户是否可以创建新清单
        if not user.can_create_list():
            from config import Config
            return error_response(msg=f'免费用户最多只能创建{Config.FREE_USER_LIST_LIMIT}个清单，请升级VIP'), 400

        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        cover_image = data.get('cover_image', '').strip()

        if not name:
            return error_response(msg='清单名称不能为空'), 400
        
        # 创建新清单
        new_list = List(
            user_id=current_user_id,
            name=name,
            description=description,
            cover_image=cover_image
        )
        
        db.session.add(new_list)
        db.session.commit()

        return success_response(data=new_list.to_dict())

    except Exception as e:
        db.session.rollback()
        return error_response(msg=f'创建清单失败: {str(e)}'), 500

@lists_bp.route('/<int:list_id>', methods=['GET'])
@jwt_required()
def get_list_detail(list_id):
    """获取清单详情"""
    try:
        list_item = List.query.get(list_id)
        if not list_item:
            return error_response(msg='清单不存在'), 404
        
        # 检查清单是否公开或用户是否有权限查看
        current_user_id = None
        try:
            current_user_id = get_jwt_identity()
        except:
            pass
        
        if str(list_item.user_id) != current_user_id:
            return error_response(msg='无权限查看此清单'), 403
        
        return success_response(data=list_item.to_dict(include_items=True)
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取清单详情失败: {str(e)}'
        }), 500

@lists_bp.route('/updateList/<int:list_id>', methods=['POST'])
@jwt_required()
def update_list(list_id):
    """更新清单"""
    try:
        current_user_id = get_jwt_identity()
        list_item = List.query.get(list_id)
        
        if not list_item:
            return error_response(msg='清单不存在'), 404
        
        # 检查权限
        if f'{list_item.user_id}' != current_user_id:
            return error_response(msg='无权限修改此清单'), 403
        
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return error_response(msg='清单名称不能为空'), 400
            list_item.name = name
        
        if 'description' in data:
            list_item.description = data['description'].strip()
        
        if 'cover_image' in data:
            list_item.cover_image = data['cover_image'].strip()
        
        if 'is_public' in data:
            list_item.is_public = bool(data['is_public'])
        
        db.session.commit()
        
        return success_response(data=list_item.to_dict()
        )
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新清单失败: {str(e)}'
        }), 500

@lists_bp.route('/deleteList/<int:list_id>', methods=['POST'])
@jwt_required()
def delete_list(list_id):
    """删除清单"""
    try:
        current_user_id = get_jwt_identity()
        list_item = List.query.get(list_id)
        
        if not list_item:
            return error_response(msg='清单不存在'), 404
        
        # 检查权限
        if f'{list_item.user_id}' != current_user_id:
            return error_response(msg='无权限删除此清单'), 403
        
        db.session.delete(list_item)
        db.session.commit()
        
        return success_response(msg='清单删除成功')
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除清单失败: {str(e)}'
        }), 500

@lists_bp.route('/<int:list_id>/follow', methods=['POST'])
@jwt_required()
def follow_list(list_id):
    """关注清单 - 从Square创建List副本"""
    try:
        current_user_id = get_jwt_identity()

        # 检查是否是Square ID
        square_id = request.json.get('square_id') if request.json else None

        if square_id:
            # 从Square创建List副本
            from models.square import SquareModel

            square = SquareModel.query.get(square_id)
            if not square:
                return error_response(msg='广场清单不存在'), 404

            # 检查是否是自己分享的清单
            if str(square.author_id) == str(current_user_id):
                return error_response(msg='不能关注自己分享的清单'), 400

            # 创建新的清单副本
            new_list = List(
                user_id=current_user_id,
                name=square.name,
                description=square.description,
                cover_image=square.cover_image,
                square_id=None  # 副本不关联广场
            )

            db.session.add(new_list)
            db.session.flush()  # 获取新清单的ID

            # 复制清单项
            items_list = square.get_items_list()
            for item_name in items_list:
                new_item = Item(
                    list_id=new_list.id,
                    name=item_name,
                    description='',
                    is_completed=False,  # 重置完成状态
                    completed_at=None
                )
                db.session.add(new_item)

            # 增加广场清单的关注数量
            square.increment_follow_count()

            db.session.commit()

            return success_response(
                data=new_list.to_dict(include_items=True),
                msg=f'成功关注并创建清单副本，新清单ID: {new_list.id}'
            )
        else:
            # 原有的List关注逻辑
            list_item = List.query.get(list_id)

            if not list_item:
                return error_response(msg='清单不存在'), 404

            success, message = Follow.follow_list(current_user_id, list_id)

            return jsonify({
                'success': success,
                'message': message
            }), 200 if success else 400

    except Exception as e:
        db.session.rollback()
        return error_response(msg=f'关注失败: {str(e)}'), 500

@lists_bp.route('/<int:list_id>/unfollow', methods=['POST'])
@jwt_required()
def unfollow_list(list_id):
    """取消关注清单"""
    try:
        current_user_id = get_jwt_identity()
        
        success, message = Follow.unfollow_list(current_user_id, list_id)
        
        return jsonify({
            'success': success,
            'message': message
        }), 200 if success else 400
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'取消关注失败: {str(e)}'
        }), 500

@lists_bp.route('/<int:list_id>/share-to-square', methods=['POST'])
@jwt_required()
def share_to_square(list_id):
    """分享清单到广场"""
    try:
        current_user_id = get_jwt_identity()

        # 获取清单
        list_item = List.query.get(list_id)
        if not list_item:
            return error_response(msg='清单不存在'), 404

        # 检查权限 - 只有创建者可以分享
        if str(list_item.user_id) != str(current_user_id):
            return error_response(msg='只有清单创建者可以分享到广场'), 403

        # 检查是否已经分享
        if list_item.square_id:
            return error_response(msg='该清单已经分享到广场'), 400

        # 导入SquareModel
        from models.square import SquareModel

        # 创建新的Square记录
        square = SquareModel.create_from_list(list_item)
        db.session.add(square)
        db.session.flush()  # 获取square的ID

        # 设置List的square_id
        list_item.square_id = square.id

        db.session.commit()

        return success_response(
            data={
                'list': list_item.to_dict(),
                'square': square.to_dict()
            },
            msg='成功分享到广场'
        )

    except Exception as e:
        db.session.rollback()
        return error_response(msg=f'分享失败: {str(e)}'), 500

@lists_bp.route('/<int:list_id>/unshare-from-square', methods=['POST'])
@jwt_required()
def unshare_from_square(list_id):
    """取消分享清单到广场"""
    try:
        current_user_id = get_jwt_identity()

        # 获取清单
        list_item = List.query.get(list_id)
        if not list_item:
            return error_response(msg='清单不存在'), 404

        # 检查权限 - 只有创建者可以取消分享
        if str(list_item.user_id) != str(current_user_id):
            return error_response(msg='只有清单创建者可以取消分享'), 403

        # 检查是否已经分享
        if not list_item.square_id:
            return error_response(msg='该清单未分享到广场'), 400

        # 导入SquareModel
        from models.square import SquareModel

        # 删除对应的Square记录
        square = SquareModel.query.get(list_item.square_id)
        if square:
            db.session.delete(square)

        # 清除List的square_id
        list_item.square_id = None

        db.session.commit()

        return success_response(
            data=list_item.to_dict(),
            msg='已取消分享到广场'
        )

    except Exception as e:
        db.session.rollback()
        return error_response(msg=f'取消分享失败: {str(e)}'), 500
