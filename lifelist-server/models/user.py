"""
User model
"""
from datetime import datetime
from app import db

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, index=True)
    nickname = db.Column(db.String(50), nullable=False)
    avatar = db.Column(db.String(255))
    birthdate = db.Column(db.Date)
    is_vip = db.Column(db.<PERSON>, default=False, nullable=False)
    huawei_openid = db.Column(db.String(100), unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    lists = db.relationship('List', backref='creator', lazy='dynamic', cascade='all, delete-orphan')
    follows = db.relationship('Follow', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.nickname}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'uuid': self.uuid,
            'nickname': self.nickname,
            'avatar': self.avatar,
            'birthdate': self.birthdate.isoformat() if self.birthdate else None,
            'is_vip': self.is_vip,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def get_list_count(self):
        """获取用户创建的清单数量"""
        return self.lists.count()
    
    def can_create_list(self):
        """检查用户是否可以创建新清单"""
        if self.is_vip:
            return True
        from config import Config
        return self.get_list_count() < Config.FREE_USER_LIST_LIMIT
    
    @staticmethod
    def find_by_uuid(uuid):
        """根据UUID查找用户"""
        return User.query.filter_by(uuid=uuid).first()
    
    @staticmethod
    def find_by_huawei_openid(openid):
        """根据华为OpenID查找用户"""
        return User.query.filter_by(huawei_openid=openid).first()
