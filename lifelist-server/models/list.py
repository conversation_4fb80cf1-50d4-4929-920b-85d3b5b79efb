"""
List model
"""
from datetime import datetime
from app import db

class List(db.Model):
    """清单表"""
    __tablename__ = 'lists'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    cover_image = db.Column(db.String(255))
    square_id = db.Column(db.Integer, db.ForeignKey('squares.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    items = db.relationship('Item', backref='list', lazy='dynamic', cascade='all, delete-orphan')
    square = db.relationship('SquareModel', backref='source_list', foreign_keys=[square_id])

    def __repr__(self):
        return f'<List {self.name}>'
    
    def to_dict(self, include_items=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'cover_image': self.cover_image,
            'square_id': self.square_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'completed_count': self.get_completed_count(),
            'total_count': self.get_total_count(),
            'progress': self.get_progress()
        }
        
        if include_items:
            result['items'] = [item.to_dict() for item in self.items.order_by('created_at')]
        
        return result
    
    def get_completed_count(self):
        """获取已完成子项数量"""
        return self.items.filter_by(is_completed=True).count()
    
    def get_total_count(self):
        """获取总子项数量"""
        return self.items.count()

    def get_progress(self):
        """获取完成进度（0-1）"""
        total = self.get_total_count()
        if total == 0:
            return 0.0
        return self.get_completed_count() / total

    @staticmethod
    def get_user_lists(user_id):
        """获取用户的清单（创建的和关注的）"""
        # 用户创建的清单
        created_lists = List.query.order_by(
            List.created_at.desc()
        ).filter_by(user_id=user_id).all()
        
        # 用户关注的清单
        # from models.follow import Follow
        # followed_list_ids = [f.list_id for f in Follow.query.filter_by(user_id=user_id).all()]
        # followed_lists = List.query.filter(List.id.in_(followed_list_ids)).all() if followed_list_ids else []
        
        # 合并并去重
        # all_lists = created_lists + [l for l in followed_lists if l not in created_lists]
        return created_lists
