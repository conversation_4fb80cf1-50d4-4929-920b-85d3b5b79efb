"""
Item model
"""
from datetime import datetime
from app import db

class Item(db.Model):
    """子项表"""
    __tablename__ = 'items'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    list_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON>('lists.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    is_completed = db.Column(db.<PERSON>, default=False, nullable=False)
    sort_order = db.Column(db.Integer, default=0)  # 排序字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<Item {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'list_id': self.list_id,
            'name': self.name,
            'is_completed': self.is_completed,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def toggle_completion(self):
        """切换完成状态"""
        self.is_completed = not self.is_completed
        self.updated_at = datetime.utcnow()
        return self.is_completed
