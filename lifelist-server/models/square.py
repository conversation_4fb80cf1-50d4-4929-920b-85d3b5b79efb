"""
Square model - 广场清单模型
"""
from datetime import datetime
from app import db
import json


class SquareModel(db.Model):
    """广场清单表"""
    __tablename__ = 'squares'

    id = db.Column(db.Integer, primary_key=True)
    original_list_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('lists.id'), nullable=False)  # 原始清单ID
    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 作者ID
    name = db.Column(db.String(100), nullable=False)  # 清单名称
    description = db.Column(db.Text)  # 清单描述
    cover_image = db.Column(db.String(255))  # 封面图片
    items = db.Column(db.Text)  # 清单项目（JSON格式存储）
    follow_count = db.Column(db.Integer, default=0, nullable=False)  # 关注数量
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关系
    original_list = db.relationship('List', backref='square_records', foreign_keys=[original_list_id])
    author = db.relationship('User', backref='authored_squares', foreign_keys=[author_id])

    def __repr__(self):
        return f'<Square {self.name}>'

    def to_dict(self):
        """转换为字典"""
        # 解析items JSON
        items_list = []
        if self.items:
            try:
                items_list = json.loads(self.items)
            except:
                items_list = []

        return {
            'id': self.id,
            'original_list_id': self.original_list_id,
            'author_id': self.author_id,
            'name': self.name,
            'description': self.description,
            'cover_image': self.cover_image,
            'items': items_list,
            'follow_count': self.follow_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'total_count': len(items_list)
        }

    def get_items_list(self):
        """获取清单项目列表"""
        if self.items:
            try:
                return json.loads(self.items)
            except:
                return []
        return []

    def set_items_list(self, items_list):
        """设置清单项目列表"""
        self.items = json.dumps(items_list, ensure_ascii=False)

    def increment_follow_count(self):
        """增加关注数量"""
        self.follow_count += 1
        db.session.commit()

    @staticmethod
    def create_from_list(list_obj):
        """从List对象创建Square记录"""
        # 获取清单项目的名称列表
        items_names = [item.name for item in list_obj.items.all()]

        square = SquareModel(
            original_list_id=list_obj.id,
            author_id=list_obj.user_id,
            name=list_obj.name,
            description=list_obj.description,
            cover_image=list_obj.cover_image,
            items=json.dumps(items_names, ensure_ascii=False)
        )

        return square
