"""
Follow model
"""
from datetime import datetime
from app import db

class Follow(db.Model):
    """关注表"""
    __tablename__ = 'follows'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    square_id = db.Column(db.Integer, db.<PERSON>ey('squares.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 移除唯一约束，允许多次follow同一个清单
    
    def __repr__(self):
        return f'<Follow user_id={self.user_id} square_id={self.square_id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'list_id': self.square_id,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def is_following(user_id, list_id):
        """检查用户是否关注了指定清单"""
        return Follow.query.filter_by(user_id=user_id, list_id=list_id).first() is not None
    
    @staticmethod
    def follow_list(user_id, list_id):
        """关注清单 - 创建一个新的清单副本"""
        from models.list import List
        from models.item import Item

        try:
            # 获取原始清单
            original_list = List.query.get(list_id)
            if not original_list:
                return False, "原始清单不存在"

            # 检查是否是自己的清单
            if str(original_list.user_id) == str(user_id):
                return False, "不能关注自己的清单"

            # 创建新的清单副本
            new_list = List(
                user_id=user_id,
                name=original_list.name,
                description=original_list.description,
                cover_image=original_list.cover_image,
                is_public=False,  # 默认设为私有
                is_shared_to_square=False,  # 不分享到广场
                original_list_id=list_id  # 记录来源
            )

            db.session.add(new_list)
            db.session.flush()  # 获取新清单的ID

            # 复制清单项
            original_items = Item.query.filter_by(list_id=list_id).all()
            for item in original_items:
                new_item = Item(
                    list_id=new_list.id,
                    name=item.name,
                    description=item.description,
                    is_completed=False,  # 重置完成状态
                    completed_at=None
                )
                db.session.add(new_item)

            # 创建关注记录
            follow = Follow(user_id=user_id, list_id=list_id)
            db.session.add(follow)

            db.session.commit()
            return True, f"成功关注并创建清单副本，新清单ID: {new_list.id}"

        except Exception as e:
            db.session.rollback()
            return False, f"关注失败: {str(e)}"
    
    @staticmethod
    def unfollow_list(user_id, list_id):
        """取消关注清单"""
        follow = Follow.query.filter_by(user_id=user_id, list_id=list_id).first()
        if not follow:
            return False, "未关注此清单"
        
        db.session.delete(follow)
        try:
            db.session.commit()
            return True, "取消关注成功"
        except Exception as e:
            db.session.rollback()
            return False, f"取消关注失败: {str(e)}"
