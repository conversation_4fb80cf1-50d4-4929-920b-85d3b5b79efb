# 文件上传API文档

## 概述

LifeList后端现在支持文件上传功能，主要用于用户头像和清单背景图的上传。

## 基础信息

- **基础URL**: `http://your-domain.com/api/v1/common`
- **认证方式**: JWT Bearer Token
- **支持的文件格式**: PNG, JPG, JPEG, GIF
- **最大文件大小**: 16MB
- **文件存储**: 本地文件系统（可扩展为云存储）

## API接口

### 1. 获取上传配置信息

**接口**: `GET /upload/info`

**描述**: 获取文件上传的配置信息，包括最大文件大小和支持的文件类型。

**请求头**:
```
Authorization: Bearer <your_jwt_token>
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取上传配置成功",
  "data": {
    "max_size_mb": 16,
    "allowed_extensions": ["png", "jpg", "jpeg", "gif"],
    "upload_folder": "uploads"
  },
  "serial_number": "1234567890"
}
```

### 2. 通用文件上传

**接口**: `POST /upload`

**描述**: 通用文件上传接口，适用于清单背景图等文件上传。

**请求头**:
```
Authorization: Bearer <your_jwt_token>
Content-Type: multipart/form-data
```

**请求参数**:
- `file` (文件): 要上传的图片文件

**响应示例**:
```json
{
  "code": 1,
  "msg": "文件上传成功",
  "data": {
    "url": "/static/uploads/2025/08/21/adbeac0b12a2460283ec0bb905b79892.png",
    "filename": "adbeac0b12a2460283ec0bb905b79892.png",
    "original_filename": "background.png",
    "size": 1024,
    "upload_time": "2025-08-21T06:31:32.611835"
  },
  "serial_number": "1234567890"
}
```

### 3. 头像上传

**接口**: `POST /upload/avatar`

**描述**: 专门用于用户头像上传的接口，文件会保存在专门的头像文件夹中。

**请求头**:
```
Authorization: Bearer <your_jwt_token>
Content-Type: multipart/form-data
```

**请求参数**:
- `file` (文件): 要上传的头像图片文件

**响应示例**:
```json
{
  "code": 1,
  "msg": "头像上传成功",
  "data": {
    "url": "/static/uploads/avatars/avatar_6_0eb65b910e0f45e69182b42fd3b85781.png",
    "filename": "avatar_6_0eb65b910e0f45e69182b42fd3b85781.png",
    "original_filename": "avatar.png",
    "size": 2048,
    "upload_time": "2025-08-21T06:31:32.611835"
  },
  "serial_number": "1234567890"
}
```

## 文件访问

上传成功后，文件可以通过以下URL访问：

```
http://your-domain.com/static/uploads/{path}
```

例如：
- 通用文件: `http://your-domain.com/static/uploads/2025/08/21/filename.png`
- 头像文件: `http://your-domain.com/static/uploads/avatars/avatar_123_filename.png`

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 没有选择文件 | 确保请求中包含file字段 |
| 400 | 不支持的文件类型 | 只支持png, jpg, jpeg, gif格式 |
| 401 | 未授权 | 检查JWT token是否有效 |
| 413 | 文件过大 | 文件大小不能超过16MB |
| 500 | 服务器错误 | 检查服务器日志 |

### 错误响应示例

```json
{
  "code": 0,
  "msg": "不支持的文件类型，支持的类型：png, jpg, jpeg, gif",
  "data": null,
  "serial_number": "1234567890"
}
```

## 使用示例

### JavaScript/Fetch

```javascript
// 上传文件
const uploadFile = async (file, token) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/v1/common/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  return await response.json();
};
```

### Python/Requests

```python
import requests

def upload_file(file_path, token):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.post(
            'http://localhost:8003/api/v1/common/upload',
            files=files,
            headers=headers
        )
    return response.json()
```

### Flutter/Dart

```dart
import 'package:dio/dio.dart';

Future<Map<String, dynamic>> uploadFile(String filePath, String token) async {
  final dio = Dio();
  final formData = FormData.fromMap({
    'file': await MultipartFile.fromFile(filePath),
  });
  
  final response = await dio.post(
    '/api/v1/common/upload',
    data: formData,
    options: Options(
      headers: {'Authorization': 'Bearer $token'},
    ),
  );
  
  return response.data;
}
```

## 文件组织结构

```
uploads/
├── 2025/
│   └── 08/
│       └── 21/
│           └── adbeac0b12a2460283ec0bb905b79892.png
└── avatars/
    └── avatar_6_0eb65b910e0f45e69182b42fd3b85781.png
```

- 通用文件按日期分文件夹存储：`uploads/YYYY/MM/DD/`
- 头像文件存储在专门的文件夹：`uploads/avatars/`
- 文件名使用UUID确保唯一性

## 安全考虑

1. **文件类型验证**: 只允许图片格式文件
2. **文件大小限制**: 防止大文件攻击
3. **JWT认证**: 需要有效的用户token
4. **文件名安全**: 使用UUID生成安全的文件名
5. **路径安全**: 防止路径遍历攻击

## 部署注意事项

1. **静态文件服务**: 生产环境建议使用Nginx等服务器处理静态文件
2. **文件存储**: 可以扩展为云存储（如AWS S3、阿里云OSS等）
3. **CDN加速**: 建议使用CDN加速文件访问
4. **备份策略**: 定期备份上传的文件
5. **清理策略**: 定期清理未使用的文件
