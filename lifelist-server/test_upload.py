#!/usr/bin/env python3
"""
测试文件上传功能的脚本
"""
import requests
import json
import os
from io import BytesIO
from PIL import Image

# 服务器配置
BASE_URL = "http://localhost:8003"
API_BASE = f"{BASE_URL}/api/v1"

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

def test_guest_login():
    """测试游客登录获取token"""
    print("🔐 测试游客登录...")

    headers = {'Content-Type': 'application/json'}
    data = {}  # 空数据，让服务器生成新的UUID
    response = requests.post(f"{API_BASE}/auth/guest", json=data, headers=headers)

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 1:
            token = data['data']['token']
            print(f"✅ 登录成功，获取到token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {data.get('msg')}")
            return None
    else:
        print(f"❌ 登录请求失败: {response.status_code}")
        return None

def test_upload_info(token):
    """测试获取上传配置信息"""
    print("\n📋 测试获取上传配置...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f"{API_BASE}/common/upload/info", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 1:
            print(f"✅ 获取上传配置成功:")
            print(f"   最大文件大小: {data['data']['max_size_mb']}MB")
            print(f"   支持的文件类型: {data['data']['allowed_extensions']}")
            print(f"   上传文件夹: {data['data']['upload_folder']}")
            return True
        else:
            print(f"❌ 获取配置失败: {data.get('msg')}")
            return False
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return False

def test_file_upload(token):
    """测试文件上传"""
    print("\n📤 测试文件上传...")
    
    # 创建测试图片
    test_image = create_test_image()
    
    headers = {'Authorization': f'Bearer {token}'}
    files = {'file': ('test_image.png', test_image, 'image/png')}
    
    response = requests.post(f"{API_BASE}/common/upload", headers=headers, files=files)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 1:
            print(f"✅ 文件上传成功:")
            print(f"   文件URL: {data['data']['url']}")
            print(f"   文件名: {data['data']['filename']}")
            print(f"   原始文件名: {data['data']['original_filename']}")
            print(f"   文件大小: {data['data']['size']} bytes")
            print(f"   上传时间: {data['data']['upload_time']}")
            return data['data']['url']
        else:
            print(f"❌ 上传失败: {data.get('msg')}")
            return None
    else:
        print(f"❌ 上传请求失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   错误信息: {error_data.get('msg', '未知错误')}")
        except:
            print(f"   响应内容: {response.text}")
        return None

def test_avatar_upload(token):
    """测试头像上传"""
    print("\n👤 测试头像上传...")
    
    # 创建测试头像
    test_image = create_test_image()
    
    headers = {'Authorization': f'Bearer {token}'}
    files = {'file': ('avatar.png', test_image, 'image/png')}
    
    response = requests.post(f"{API_BASE}/common/upload/avatar", headers=headers, files=files)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 1:
            print(f"✅ 头像上传成功:")
            print(f"   头像URL: {data['data']['url']}")
            print(f"   文件名: {data['data']['filename']}")
            return data['data']['url']
        else:
            print(f"❌ 头像上传失败: {data.get('msg')}")
            return None
    else:
        print(f"❌ 头像上传请求失败: {response.status_code}")
        return None

def test_file_access(file_url):
    """测试文件访问"""
    print(f"\n🌐 测试文件访问: {file_url}")
    
    full_url = f"{BASE_URL}{file_url}"
    response = requests.get(full_url)
    
    if response.status_code == 200:
        print(f"✅ 文件访问成功，文件大小: {len(response.content)} bytes")
        return True
    else:
        print(f"❌ 文件访问失败: {response.status_code}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试文件上传功能")
    print("=" * 50)
    
    # 1. 登录获取token
    token = test_guest_login()
    if not token:
        print("❌ 无法获取token，测试终止")
        return
    
    # 2. 获取上传配置
    if not test_upload_info(token):
        print("⚠️ 获取上传配置失败，但继续测试")
    
    # 3. 测试普通文件上传
    file_url = test_file_upload(token)
    if file_url:
        # 4. 测试文件访问
        test_file_access(file_url)
    
    # 5. 测试头像上传
    avatar_url = test_avatar_upload(token)
    if avatar_url:
        # 6. 测试头像访问
        test_file_access(avatar_url)
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    # 检查PIL是否安装
    try:
        from PIL import Image
    except ImportError:
        print("❌ 需要安装Pillow库: pip install Pillow")
        exit(1)
    
    main()
