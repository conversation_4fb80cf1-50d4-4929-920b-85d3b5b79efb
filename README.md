# 美瞬人生清单 (LifeList)

一个帮助用户记录和追踪人生各类清单的应用程序，包括Flutter移动端、Python后端API和React管理后台。

## 项目结构

```
lifelist/
├── lifelist-app/          # Flutter移动端应用
├── lifelist-server/       # Python Flask后端API
├── lifelist-admin/        # React管理后台
└── README.md
```

## 功能特性

### 移动端 (Flutter)
- 华为OAuth2登录 / 游客模式
- 清单创建、编辑、删除
- 子项管理（添加、完成、删除）
- 清单关注功能
- 热门清单浏览
- VIP功能（无限制创建清单）

### 后端 (Python Flask)
- RESTful API设计
- JWT身份验证
- SQLite数据库
- 华为OAuth2集成
- 数据导出功能
- 管理员后台API

### 管理后台 (React)
- 用户管理（VIP状态切换）
- 清单管理（公开/私有状态）
- 子项管理
- 数据统计仪表板
- CSV数据导出

## 快速开始

### 1. 后端服务器

```bash
cd lifelist-server

# 安装依赖
pip3 install -r requirements.txt

# 启动服务器
python3 main.py
```

服务器将在 http://localhost:8002 启动

### 2. Flutter移动端

```bash
cd lifelist-app

# 获取依赖
flutter pub get

# 运行应用
flutter run
```

### 3. React管理后台

```bash
cd lifelist-admin

# 安装依赖
npm install

# 启动开发服务器
npm start
```

管理后台将在 http://localhost:3000 启动

## API文档

### 响应格式

所有API响应都采用统一的格式：

```json
{
  "code": 1,                    // 响应码：1=成功，0=失败
  "msg": "Success",             // 响应消息
  "serial_number": "1234567890", // 时间戳（毫秒）
  "data": {                     // 响应数据
    // 具体数据内容
  }
}
```

### 认证相关
- `POST /api/v1/auth/huawei` - 华为OAuth2登录
- `POST /api/v1/auth/guest` - 游客登录
- `POST /api/v1/auth/refresh` - 刷新token

### 清单相关
- `GET /api/v1/lists/recommended` - 获取推荐清单
- `GET /api/v1/lists/my` - 获取我的清单
- `POST /api/v1/lists` - 创建清单
- `GET /api/v1/lists/{id}` - 获取清单详情
- `PUT /api/v1/lists/{id}` - 更新清单
- `DELETE /api/v1/lists/{id}` - 删除清单
- `POST /api/v1/lists/{id}/follow` - 关注清单
- `POST /api/v1/lists/{id}/unfollow` - 取消关注

### 子项相关
- `POST /api/v1/items` - 添加子项
- `PUT /api/v1/items/{id}` - 更新子项
- `DELETE /api/v1/items/{id}` - 删除子项
- `POST /api/v1/items/{id}/toggle` - 切换完成状态

### 用户相关
- `GET /api/v1/user/profile` - 获取用户信息
- `PUT /api/v1/user/profile` - 更新用户信息
- `GET /api/v1/user/vip` - 获取VIP状态
- `POST /api/v1/user/vip/upgrade` - 升级VIP

### API响应示例

#### 游客登录
```bash
curl -X POST "http://localhost:8002/api/v1/auth/guest" \
  -H "Content-Type: application/json" \
  -d '{"uuid": "test-uuid"}'
```

响应：
```json
{
  "code": 1,
  "msg": "Success",
  "serial_number": "1755635121561",
  "data": {
    "user_id": 2,
    "uuid": "test-uuid",
    "nickname": "游客用户",
    "avatar": null,
    "is_vip": false,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### 获取推荐清单
```bash
curl -X GET "http://localhost:8002/api/v1/lists/recommended"
```

响应：
```json
{
  "code": 1,
  "msg": "Success",
  "serial_number": "1755635112695",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "我去过的中国城市",
        "description": "记录我去过的所有中国城市",
        "cover_image": "https://example.com/cities.jpg",
        "completed_count": 3,
        "total_count": 10,
        "follow_count": 25,
        "progress": 0.3,
        "created_at": "2024-01-01T00:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 2,
      "pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

## 管理后台

访问 http://localhost:8002/admin/login

默认管理员账号：
- 用户名：admin
- 密码：admin123

## 数据库

项目使用SQLite数据库，数据库文件会自动创建在 `lifelist-server/lifelist.db`

### 数据表结构

- `users` - 用户表
- `lists` - 清单表
- `items` - 子项表
- `follows` - 关注表

## 配置

### 环境变量

在 `lifelist-server` 目录下创建 `.env` 文件：

```env
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret
HUAWEI_CLIENT_ID=your-huawei-client-id
HUAWEI_CLIENT_SECRET=your-huawei-client-secret
HUAWEI_REDIRECT_URI=your-redirect-uri
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

### Flutter配置

在 `lifelist-app/lib/services/api_service.dart` 中修改API地址：

```dart
static const String baseUrl = 'http://your-server:8002/api/v1';
```

## 部署

### 后端部署

1. 使用Gunicorn部署Flask应用
2. 配置Nginx反向代理
3. 使用PostgreSQL替换SQLite（生产环境）

### 前端部署

1. Flutter: 构建APK/IPA文件
2. React: 构建静态文件并部署到CDN

## 开发说明

### 技术栈

- **移动端**: Flutter, Dart
- **后端**: Python, Flask, SQLAlchemy, JWT
- **管理后台**: React, Ant Design, Axios
- **数据库**: SQLite (开发) / PostgreSQL (生产)

### 开发规范

1. 遵循RESTful API设计原则
2. 使用JWT进行身份验证
3. 前端使用组件化开发
4. 后端使用蓝图组织路由
5. 数据库使用ORM操作

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
